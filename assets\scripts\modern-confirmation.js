/**
 * Modern Confirmation Page Manager
 * Handles order confirmation display, tracking updates, and user interactions
 * with modern ES6+ features and enhanced user experience
 */

import { CURRENCY_CONFIG, formatPrice } from './config/currency.js';
import Analytics from './services/Analytics.js';

class ModernConfirmationManager {
    constructor() {
        this.elements = {};
        this.state = {
            orderId: null,
            orderData: null,
            trackingInterval: null
        };
        
        this.init();
    }

    /**
     * Initialize the confirmation manager
     */
    async init() {
        try {
            this.cacheElements();
            this.extractOrderId();
            await this.loadOrderData();
            this.bindEvents();
            this.updateDisplay();
            this.initializeTracking();
            this.initializeAccessibility();
            this.trackOrderConfirmation();
        } catch (error) {
            console.error('Failed to initialize confirmation:', error);
            this.showError('Failed to load order details. Please contact support.');
        }
    }

    /**
     * Cache DOM elements for better performance
     */
    cacheElements() {
        this.elements = {
            // Order details
            orderNumber: document.querySelector('.order-number'),
            orderStatus: document.querySelector('.order-status'),
            orderDate: document.querySelector('.order-date'),
            estimatedDelivery: document.querySelector('.estimated-delivery'),
            
            // Order items
            itemsList: document.querySelector('.items-list'),
            
            // Order totals
            orderSubtotal: document.querySelector('.order-subtotal'),
            orderTax: document.querySelector('.order-tax'),
            orderDelivery: document.querySelector('.order-delivery'),
            orderTotal: document.querySelector('.order-total'),
            deliveryFeeLine: document.querySelector('.delivery-fee-line'),
            
            // Customer info
            customerName: document.querySelector('.customer-name'),
            customerEmail: document.querySelector('.customer-email'),
            customerPhone: document.querySelector('.customer-phone'),
            
            // Delivery info
            deliveryAddress: document.querySelector('.delivery-address'),
            
            // Timeline
            timelineItems: document.querySelectorAll('.timeline-item'),
            
            // Share buttons
            shareButtons: document.querySelectorAll('.share-btn'),
            
            // Toast container
            toastContainer: document.querySelector('#toast-container'),
            
            // Cart count
            cartCount: document.querySelector('#cart-count')
        };
    }

    /**
     * Extract order ID from URL parameters
     */
    extractOrderId() {
        const urlParams = new URLSearchParams(window.location.search);
        this.state.orderId = urlParams.get('orderId');
        
        if (!this.state.orderId) {
            // Redirect to home if no order ID
            this.showError('No order ID found. Redirecting to home...');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }
    }

    /**
     * Load order data
     */
    async loadOrderData() {
        if (!this.state.orderId) return;
        
        try {
            // Try to load from API first
            try {
                const response = await fetch(`/api/orders/${this.state.orderId}`);
                if (response.ok) {
                    this.state.orderData = await response.json();
                    return;
                }
            } catch (apiError) {
                console.warn('API unavailable, using mock data:', apiError);
            }
            
            // Mock order data for demo
            this.state.orderData = {
                id: this.state.orderId,
                status: 'confirmed',
                date: new Date().toISOString(),
                estimatedDelivery: new Date(Date.now() + 45 * 60 * 1000).toISOString(),
                items: this.getMockOrderItems(),
                totals: {
                    subtotal: 2500,
                    tax: 187.5,
                    deliveryFee: 500,
                    total: 3187.5
                },
                customer: {
                    name: 'John Doe',
                    email: '<EMAIL>',
                    phone: '+234 ************'
                },
                delivery: {
                    method: 'delivery',
                    address: '123 Main Street, Victoria Island, Lagos',
                    notes: 'Please call when you arrive'
                },
                payment: {
                    method: 'card',
                    status: 'paid'
                }
            };
            
        } catch (error) {
            console.error('Failed to load order data:', error);
            throw error;
        }
    }

    /**
     * Get mock order items (fallback when API is unavailable)
     */
    getMockOrderItems() {
        try {
            // Try to get from localStorage first
            const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
            if (cartItems.length > 0) {
                return cartItems;
            }
        } catch (error) {
            console.warn('Failed to load cart items from localStorage:', error);
        }
        
        // Fallback mock items
        return [
            {
                id: 1,
                title: 'Jollof Rice with Chicken',
                description: 'Spicy Nigerian jollof rice with grilled chicken',
                price: '₦1,500.00',
                quantity: 1,
                image: 'assets/images/jollof-rice.jpg'
            },
            {
                id: 2,
                title: 'Pepper Soup',
                description: 'Traditional Nigerian pepper soup with fish',
                price: '₦1,000.00',
                quantity: 1,
                image: 'assets/images/pepper-soup.jpg'
            }
        ];
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Share buttons
        this.elements.shareButtons.forEach(btn => {
            btn.addEventListener('click', this.handleShare.bind(this));
        });
        
        // Mobile menu toggle
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        mobileMenuToggle?.addEventListener('click', this.toggleMobileMenu.bind(this));
        
        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));
        
        // Print functionality
        const printBtn = document.querySelector('button[onclick="window.print()"]');
        if (printBtn) {
            printBtn.onclick = null; // Remove inline onclick
            printBtn.addEventListener('click', this.handlePrint.bind(this));
        }
    }

    /**
     * Update display with order data
     */
    updateDisplay() {
        if (!this.state.orderData) return;
        
        const order = this.state.orderData;
        
        // Update order details
        if (this.elements.orderNumber) {
            this.elements.orderNumber.textContent = `#${order.id}`;
        }
        
        if (this.elements.orderDate) {
            this.elements.orderDate.textContent = this.formatDate(order.date);
        }
        
        if (this.elements.estimatedDelivery) {
            this.elements.estimatedDelivery.textContent = this.formatDeliveryTime(order.estimatedDelivery);
        }
        
        // Update order items
        this.updateOrderItems(order.items);
        
        // Update totals
        this.updateOrderTotals(order.totals);
        
        // Update customer info
        this.updateCustomerInfo(order.customer);
        
        // Update delivery info
        this.updateDeliveryInfo(order.delivery);
        
        // Update cart count (should be 0 after order)
        if (this.elements.cartCount) {
            this.elements.cartCount.textContent = '0';
        }
    }

    /**
     * Update order items display
     */
    updateOrderItems(items) {
        if (!this.elements.itemsList || !items) return;
        
        this.elements.itemsList.innerHTML = '';
        
        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'order-item';
            
            const price = parseFloat(item.price?.replace(/[^\d.-]/g, '') || 0);
            const totalPrice = price * (item.quantity || 1);
            
            itemElement.innerHTML = `
                <img src="${item.image || 'assets/images/placeholder-food.jpg'}" 
                     alt="${item.title || 'Food item'}" 
                     class="item-image"
                     loading="lazy">
                <div class="item-details">
                    <div class="item-name">${item.title || 'Unknown Item'}</div>
                    <div class="item-description">${item.description || ''}</div>
                </div>
                <div class="item-quantity">${item.quantity || 1}</div>
                <div class="item-price">${formatPrice(totalPrice)}</div>
            `;
            
            this.elements.itemsList.appendChild(itemElement);
        });
    }

    /**
     * Update order totals display
     */
    updateOrderTotals(totals) {
        if (!totals) return;
        
        if (this.elements.orderSubtotal) {
            this.elements.orderSubtotal.textContent = formatPrice(totals.subtotal);
        }
        if (this.elements.orderTax) {
            this.elements.orderTax.textContent = formatPrice(totals.tax);
        }
        if (this.elements.orderDelivery) {
            this.elements.orderDelivery.textContent = formatPrice(totals.deliveryFee);
        }
        if (this.elements.orderTotal) {
            this.elements.orderTotal.textContent = formatPrice(totals.total);
        }
        
        // Hide delivery fee if it's 0 (pickup order)
        if (this.elements.deliveryFeeLine && totals.deliveryFee === 0) {
            this.elements.deliveryFeeLine.style.display = 'none';
        }
    }

    /**
     * Update customer information display
     */
    updateCustomerInfo(customer) {
        if (!customer) return;
        
        if (this.elements.customerName) {
            this.elements.customerName.textContent = customer.name || '--';
        }
        if (this.elements.customerEmail) {
            this.elements.customerEmail.textContent = customer.email || '--';
        }
        if (this.elements.customerPhone) {
            this.elements.customerPhone.textContent = customer.phone || '--';
        }
    }

    /**
     * Update delivery information display
     */
    updateDeliveryInfo(delivery) {
        if (!delivery) return;
        
        if (this.elements.deliveryAddress) {
            this.elements.deliveryAddress.textContent = delivery.address || '--';
        }
        
        // Update delivery method icon and text
        const methodIcon = document.querySelector('.method-icon i');
        const methodType = document.querySelector('.method-type');
        
        if (delivery.method === 'pickup') {
            if (methodIcon) methodIcon.className = 'fas fa-store';
            if (methodType) methodType.textContent = 'Store Pickup';
        }
    }

    /**
     * Initialize order tracking
     */
    initializeTracking() {
        // Simulate order progress updates
        this.updateTrackingStatus();
        
        // Set up periodic updates (in a real app, this would be WebSocket or polling)
        this.state.trackingInterval = setInterval(() => {
            this.updateTrackingStatus();
        }, 30000); // Update every 30 seconds
    }

    /**
     * Update tracking status
     */
    updateTrackingStatus() {
        // This would normally come from the server
        // For demo, we'll simulate progress based on time
        const orderTime = new Date(this.state.orderData?.date || Date.now());
        const now = new Date();
        const minutesElapsed = (now - orderTime) / (1000 * 60);
        
        // Update timeline based on elapsed time
        if (minutesElapsed > 5) {
            this.activateTimelineItem(1); // Preparing
        }
        if (minutesElapsed > 20) {
            this.activateTimelineItem(2); // Out for delivery
        }
        if (minutesElapsed > 45) {
            this.activateTimelineItem(3); // Delivered
        }
    }

    /**
     * Activate timeline item
     */
    activateTimelineItem(index) {
        const timelineItem = this.elements.timelineItems[index];
        if (timelineItem && !timelineItem.classList.contains('completed')) {
            timelineItem.classList.add('completed');
            
            // Update time
            const timeElement = timelineItem.querySelector('.timeline-time');
            if (timeElement) {
                timeElement.textContent = 'Just now';
            }
            
            // Show notification
            const statusTexts = ['', 'Your order is being prepared', 'Your order is out for delivery', 'Your order has been delivered'];
            if (statusTexts[index]) {
                this.showToast(statusTexts[index], 'success');
            }
        }
    }

    /**
     * Handle social sharing
     */
    handleShare(e) {
        e.preventDefault();
        
        const platform = e.currentTarget.classList.contains('facebook') ? 'facebook' :
                        e.currentTarget.classList.contains('twitter') ? 'twitter' :
                        e.currentTarget.classList.contains('instagram') ? 'instagram' :
                        e.currentTarget.classList.contains('whatsapp') ? 'whatsapp' : '';
        
        const shareText = `Just ordered delicious food from Magic Menu! Order #${this.state.orderId}`;
        const shareUrl = window.location.origin;
        
        let shareLink = '';
        
        switch (platform) {
            case 'facebook':
                shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
                break;
            case 'twitter':
                shareLink = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
                break;
            case 'whatsapp':
                shareLink = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + shareUrl)}`;
                break;
            case 'instagram':
                // Instagram doesn't support direct sharing via URL
                this.showToast('Please share manually on Instagram', 'info');
                return;
        }
        
        if (shareLink) {
            window.open(shareLink, '_blank', 'width=600,height=400');
            
            // Analytics
            if (Analytics.consent) {
                Analytics.track('order_shared', {
                    platform,
                    orderId: this.state.orderId
                });
            }
        }
    }

    /**
     * Handle print functionality
     */
    handlePrint(e) {
        e.preventDefault();
        
        // Analytics
        if (Analytics.consent) {
            Analytics.track('receipt_printed', {
                orderId: this.state.orderId
            });
        }
        
        window.print();
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return 'Invalid date';
        }
    }

    /**
     * Format delivery time for display
     */
    formatDeliveryTime(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return 'TBD';
        }
    }

    /**
     * Track order confirmation for analytics
     */
    trackOrderConfirmation() {
        if (Analytics.consent && this.state.orderData) {
            Analytics.trackEcommerce('purchase_confirmed', {
                orderId: this.state.orderId,
                value: this.state.orderData.totals.total,
                items: this.state.orderData.items
            });
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        if (!this.elements.toastContainer) return;
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="fas fa-${this.getToastIcon(type)}"></i>
            <span>${message}</span>
            <button class="toast-close" aria-label="Close notification">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Close button
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.remove();
        });
        
        this.elements.toastContainer.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * Get toast icon based on type
     */
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * Handle keyboard navigation
     */
    handleKeyboardNavigation(e) {
        // Escape key to close mobile menu
        if (e.key === 'Escape') {
            const mobileMenu = document.querySelector('.nav-list');
            if (mobileMenu && mobileMenu.classList.contains('active')) {
                this.toggleMobileMenu();
            }
        }
    }

    /**
     * Toggle mobile menu
     */
    toggleMobileMenu() {
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navList = document.querySelector('.nav-list');
        
        if (mobileMenuToggle && navList) {
            const isExpanded = mobileMenuToggle.getAttribute('aria-expanded') === 'true';
            
            mobileMenuToggle.setAttribute('aria-expanded', !isExpanded);
            navList.classList.toggle('active');
            document.body.classList.toggle('menu-open');
        }
    }

    /**
     * Initialize accessibility features
     */
    initializeAccessibility() {
        // Add live region for status updates
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);
        
        this.liveRegion = liveRegion;
        
        // Announce order confirmation
        setTimeout(() => {
            this.announce(`Order ${this.state.orderId} confirmed successfully`);
        }, 2000);
    }

    /**
     * Announce to screen readers
     */
    announce(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Cleanup when page is unloaded
     */
    cleanup() {
        if (this.state.trackingInterval) {
            clearInterval(this.state.trackingInterval);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const confirmationManager = new ModernConfirmationManager();
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        confirmationManager.cleanup();
    });
});

// Export for testing
export default ModernConfirmationManager;
