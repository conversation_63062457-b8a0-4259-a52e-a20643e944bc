<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'nonce-{RANDOM_NONCE}';
        style-src 'self' 'unsafe-inline';
        img-src 'self' data: https:;
        connect-src 'self' https://api.magicmenu.com;
        frame-ancestors 'none';
        form-action 'self';
    ">
    <meta name="csrf-token" content="{SERVER_GENERATED_TOKEN}">
    <title>Magic Menu - Account</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/modern-base.css">
    <link rel="stylesheet" href="assets/css/modern-header.css">
    <link rel="stylesheet" href="assets/css/account.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="manifest" href="assets/images/site.webmanifest">
</head>
<body>

    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Modern Header -->
    <header class="header" role="banner">
        <div class="header-content">
            <a href="index.html" class="logo" aria-label="Magic Menu - Go to homepage">
                <img src="assets/images/logo.png" alt="Magic Menu Logo" width="40" height="40">
                <span class="logo-text">Magic Menu</span>
            </a>

            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="menu.html" class="nav-link">Menu</a></li>
                    <li><a href="modern-cart.html" class="nav-link">Order</a></li>
                    <li><a href="account.html" class="nav-link">Account</a></li>
                    <li><a href="contact.html" class="nav-link">Contact</a></li>
                </ul>
            </nav>

            <div class="cart-icon-wrapper">
                <button class="cart-icon" aria-label="View cart" aria-describedby="cart-count">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span id="cart-count" class="cart-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="auth-container">
            <div class="auth-wrapper">
                <div class="auth-switcher">
                    <button class="switch-btn active" data-form="login">Sign In</button>
                    <button class="switch-btn" data-form="signup">Sign Up</button>
                    <div class="switch-indicator"></div>
                </div>

                <div class="forms-wrapper">
                    <!-- Login Form -->
                    <form id="loginForm" class="auth-form active" novalidate>
                        <div class="form-group floating">
                            <input type="email" id="login-email" required>
                            <label for="login-email">
                                <i class="fas fa-envelope"></i> Email
                            </label>
                            <div class="error-message"></div>
                        </div>
                        <div class="form-group floating">
                            <input type="password" id="login-password" required>
                            <label for="login-password">
                                <i class="fas fa-lock"></i> Password
                            </label>
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                            <div class="error-message"></div>
                        </div>
                        <div class="form-group checkbox">
                            <input type="checkbox" id="remember-me">
                            <label for="remember-me">Remember me</label>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <span class="btn-text">Sign In</span>
                            <span class="btn-loader"></span>
                        </button>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </form>

                    <!-- Signup Form -->
                    <form id="signupForm" class="auth-form" novalidate>
                        <div class="form-group floating">
                            <input type="text" id="signup-name" required>
                            <label for="signup-name">
                                <i class="fas fa-user"></i> Full Name
                            </label>
                            <div class="error-message"></div>
                        </div>
                        <div class="form-group floating">
                            <input type="email" id="signup-email" required>
                            <label for="signup-email">
                                <i class="fas fa-envelope"></i> Email
                            </label>
                            <div class="error-message"></div>
                        </div>
                        <div class="form-group floating">
                            <input type="password" id="signup-password" required>
                            <label for="signup-password">
                                <i class="fas fa-lock"></i> Password
                            </label>
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                            <div class="error-message"></div>
                        </div>
                        <div class="form-group checkbox">
                            <input type="checkbox" id="terms" required>
                            <label for="terms">I agree to the <a href="/terms">Terms</a> and <a href="/privacy">Privacy Policy</a></label>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <span class="btn-text">Create Account</span>
                            <span class="btn-loader"></span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </main>

     <!-- Footer -->
    <footer class="footer">
        <div class="container footer-content">
            <div class="footer-section">
                <h4 class="footer-heading">Contact</h4>
                <p>123 Main Street<br>City, State 12345</p>
                <p>Phone: (*************</p>
                <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Hours</h4>
                <p>Mon-Fri: 11am - 10pm</p>
                <p>Sat-Sun: 12pm - 11pm</p>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Links</h4>
                <ul class="footer-links"> <!-- Added class -->
                    <li><a href="terms.html" class="footer-link">Terms & Conditions</a></li> <!--Added class -->
                    <li><a href="privacy.html" class="footer-link">Privacy Policy</a></li>  <!--Added class -->
                    <li><a href="faq.html" class="footer-link">FAQ</a></li>  <!--Added class -->
                </ul>
            </div>
           <div class="footer-section">
                <h4 class="footer-heading">Follow Us</h4>
                <div class="social-links">
                    <a href="#" aria-label="facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" aria-label="twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" aria-label="instagram"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
         <div class="copyright">
            © 2025 Magic Menu. All rights reserved.
        </div>
    </footer>

    <script src="assets/scripts/account.js"></script>

    <!-- Modern Header Component -->
    <script type="module" src="assets/scripts/components/ModernHeader.js"></script>
</body>
</html>
