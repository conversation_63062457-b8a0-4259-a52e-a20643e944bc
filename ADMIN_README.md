# Magic Menu Admin Dashboard

## Overview
The Magic Menu Admin Dashboard is a comprehensive administrative interface for managing the restaurant's operations, including orders, menu items, customers, and analytics.

## Features

### 🔐 Authentication
- Secure admin login with email and password
- Session management with localStorage
- Demo credentials: `<EMAIL>` / `admin123`

### 📊 Dashboard Overview
- Real-time statistics (orders, revenue, customers, ratings)
- Recent orders table
- Popular menu items list
- Performance metrics with visual indicators

### 🛒 Order Management
- View all orders with filtering and search
- Order status management (pending, processing, completed, cancelled)
- Customer information display
- Bulk actions and pagination
- Order details and editing capabilities

### 🍽️ Menu Management
- Add, edit, and delete menu items
- Category-based organization (Local Delights, International, Beverages, Desserts)
- Image upload for menu items
- Price and description management
- Availability toggle
- Popular items tracking

### 👥 Customer Management
- Customer database with contact information
- Order history and spending analytics
- Customer status management
- Search and filter functionality
- Communication tools

### 📈 Analytics & Reports
- Sales overview charts (revenue and orders over time)
- Popular categories breakdown (doughnut chart)
- Customer demographics (age groups)
- Top performing items table
- Exportable reports
- Date range filtering

### ⚙️ Settings
- General restaurant settings (name, contact info, address)
- Currency and tax rate configuration
- User management
- Payment method settings
- Notification preferences
- Backup and restore options

## Technical Implementation

### Frontend Technologies
- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern styling with CSS Grid, Flexbox, and custom properties
- **JavaScript (ES6+)**: Modular class-based architecture
- **Chart.js**: Interactive charts and data visualization
- **Font Awesome**: Icon library for UI elements

### Key Components

#### AdminDashboard Class
Main controller class that handles:
- Authentication flow
- Navigation between sections
- Event binding and handling
- Chart initialization
- Modal management

#### Responsive Design
- Mobile-first approach
- Collapsible sidebar for smaller screens
- Adaptive grid layouts
- Touch-friendly interface elements

#### Security Features
- Content Security Policy (CSP) headers
- CSRF token integration
- Input validation and sanitization
- Secure authentication flow

### File Structure
```
admin.html              # Main admin page
assets/css/admin.css    # Admin-specific styles
assets/scripts/admin.js # Admin functionality
```

## Usage Instructions

### Accessing the Admin Dashboard
1. Navigate to `/admin.html`
2. Enter admin credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`
3. Click "Access Dashboard"

### Navigation
- Use the sidebar to navigate between sections
- Click the hamburger menu to collapse/expand sidebar
- Use the user menu in the top-right for profile and logout options

### Managing Orders
1. Go to "Orders" section
2. Use search bar to find specific orders
3. Filter by status using the dropdown
4. Click action buttons to view, edit, or delete orders
5. Use pagination to navigate through order pages

### Managing Menu Items
1. Go to "Menu Management" section
2. Click "Add New Item" to create new menu items
3. Use category tabs to filter items
4. Click action buttons on item cards to edit, delete, or toggle availability
5. Upload images using the file upload interface

### Viewing Analytics
1. Go to "Analytics & Reports" section
2. Use date range picker to filter data
3. View interactive charts for sales, categories, and demographics
4. Export reports using the export button
5. Check top performing items in the table

### Configuring Settings
1. Go to "Settings" section
2. Use the sidebar to navigate between setting categories
3. Update restaurant information, currency, and tax rates
4. Save changes using the form buttons

## Customization

### Adding New Sections
1. Add navigation item to sidebar in `admin.html`
2. Create corresponding section element
3. Add navigation handler in `admin.js`
4. Style the new section in `admin.css`

### Modifying Charts
Charts are created using Chart.js. To modify:
1. Update data in the respective chart creation methods
2. Customize colors, labels, and options
3. Add new chart types as needed

### Styling Customization
The admin interface uses CSS custom properties for easy theming:
- `--primary-color`: Main brand color
- `--secondary-color`: Secondary text color
- `--text-color`: Primary text color
- `--background-color`: Page background

## Browser Support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Future Enhancements
- Real-time notifications
- Advanced reporting features
- Multi-language support
- Role-based access control
- API integration for live data
- Mobile app companion

## Demo Data
The dashboard includes sample data for demonstration purposes:
- Mock orders with various statuses
- Sample menu items with images
- Dummy customer data
- Simulated analytics data

For production use, replace demo data with actual API calls to your backend services.
