// Debug script to test menu functionality
console.log('=== Menu Debug Script ===');

// Test 1: Check if menu elements exist
console.log('1. Checking menu elements...');
const menuContent = document.querySelector('.menu-content');
const quickViewButtons = document.querySelectorAll('.quick-view-btn');
const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
const cartIcon = document.getElementById('cartIcon');
const cartCount = document.querySelector('.cart-count');

console.log('Menu content:', menuContent ? '✓ Found' : '✗ Not found');
console.log('Quick view buttons:', quickViewButtons.length);
console.log('Add to cart buttons:', addToCartButtons.length);
console.log('Cart icon:', cartIcon ? '✓ Found' : '✗ Not found');
console.log('Cart count:', cartCount ? '✓ Found' : '✗ Not found');

// Test 2: Check if modal exists
console.log('\n2. Checking modal...');
const modal = document.getElementById('itemDetailModal');
console.log('Modal:', modal ? '✓ Found' : '✗ Not found');

if (modal) {
    const modalTitle = document.getElementById('modalTitle');
    const modalImage = document.getElementById('modalImage');
    const modalPrice = document.getElementById('modalPrice');
    const modalAddToCart = document.getElementById('modalAddToCart');

    console.log('Modal title:', modalTitle ? '✓ Found' : '✗ Not found');
    console.log('Modal image:', modalImage ? '✓ Found' : '✗ Not found');
    console.log('Modal price:', modalPrice ? '✓ Found' : '✗ Not found');
    console.log('Modal add to cart:', modalAddToCart ? '✓ Found' : '✗ Not found');
}

// Test 3: Check localStorage
console.log('\n3. Checking localStorage...');
const cartItems = localStorage.getItem('cartItems');
console.log('Cart items in localStorage:', cartItems || 'Empty');

// Test 4: Check if scripts are loaded
console.log('\n4. Checking script loading...');
console.log('MenuDetail available:', typeof MenuDetail !== 'undefined' ? '✓ Yes' : '✗ No');
console.log('CartManager available:', typeof CartManager !== 'undefined' ? '✓ Yes' : '✗ No');
console.log('Alert available:', typeof Alert !== 'undefined' ? '✓ Yes' : '✗ No');

// Test 5: Test quick view functionality
console.log('\n5. Testing quick view...');
if (quickViewButtons.length > 0) {
    const firstQuickViewBtn = quickViewButtons[0];
    const card = firstQuickViewBtn.closest('.menu-item-card');

    if (card) {
        console.log('Card data:', {
            id: card.dataset.id,
            title: card.dataset.title,
            price: card.dataset.price,
            description: card.dataset.description
        });

        // Test click event
        console.log('Simulating quick view click...');
        try {
            firstQuickViewBtn.click();
            setTimeout(() => {
                if (modal && modal.style.display === 'block') {
                    console.log('✓ Modal opened successfully');
                } else {
                    console.log('✗ Modal did not open');
                }
            }, 100);
        } catch (error) {
            console.error('Error clicking quick view:', error);
        }
    }
}

// Test 5: Test add to cart functionality
console.log('\n5. Testing add to cart...');
if (addToCartButtons.length > 0) {
    const firstAddToCartBtn = addToCartButtons[0];
    const card = firstAddToCartBtn.closest('.menu-item-card');
    
    if (card) {
        console.log('Testing add to cart for:', card.dataset.title);
        
        // Get initial cart count
        const initialCartItems = localStorage.getItem('cartItems');
        const initialCount = initialCartItems ? JSON.parse(initialCartItems).length : 0;
        
        try {
            firstAddToCartBtn.click();
            
            setTimeout(() => {
                const newCartItems = localStorage.getItem('cartItems');
                const newCount = newCartItems ? JSON.parse(newCartItems).length : 0;
                
                if (newCount > initialCount) {
                    console.log('✓ Item added to cart successfully');
                    console.log('Cart items:', JSON.parse(newCartItems));
                } else {
                    console.log('✗ Item was not added to cart');
                }
                
                // Check if cart count updated in UI
                const displayedCount = cartCount.textContent;
                console.log('Cart count in UI:', displayedCount);
            }, 100);
        } catch (error) {
            console.error('Error clicking add to cart:', error);
        }
    }
}

// Test 6: Check for JavaScript errors
console.log('\n6. Checking for errors...');
window.addEventListener('error', (e) => {
    console.error('JavaScript Error:', e.error);
});

// Test 7: Check imports
console.log('\n7. Checking imports...');
try {
    // These should be available if imports work correctly
    console.log('Testing module availability...');
    // We can't directly test imports here, but we can check if functions exist
} catch (error) {
    console.error('Import error:', error);
}

console.log('\n=== Debug Complete ===');
