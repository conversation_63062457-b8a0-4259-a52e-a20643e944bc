const Logger = require('./Logger');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

class SecurityAuditor {
    static async runNpmAudit() {
        try {
            const { stdout } = await execAsync('npm audit --json');
            const auditResults = JSON.parse(stdout);
            
            if (auditResults.vulnerabilities) {
                Logger.logSecurityEvent({
                    type: 'npm-audit',
                    vulnerabilities: auditResults.vulnerabilities,
                    timestamp: new Date()
                });
            }
            
            return auditResults;
        } catch (error) {
            Logger.logError(error, { context: 'npm-audit' });
            throw error;
        }
    }

    static async checkDependencyUpdates() {
        try {
            const { stdout } = await execAsync('npm outdated --json');
            const outdatedDeps = JSON.parse(stdout);
            
            Logger.logSecurityEvent({
                type: 'dependency-check',
                outdatedDependencies: outdatedDeps,
                timestamp: new Date()
            });
            
            return outdatedDeps;
        } catch (error) {
            Logger.logError(error, { context: 'dependency-check' });
            throw error;
        }
    }

    static scheduleAudits() {
        // Run security audits daily
        setInterval(async () => {
            await this.runNpmAudit();
            await this.checkDependencyUpdates();
        }, 24 * 60 * 60 * 1000);
    }
}

module.exports = SecurityAuditor;