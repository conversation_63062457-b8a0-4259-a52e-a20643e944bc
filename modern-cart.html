<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magic Menu - Your Order</title>
    
    <!-- Critical CSS -->
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/modern-cart.css">
    
    <!-- External Dependencies -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">
    
    <!-- Favicons -->
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="manifest" href="assets/images/site.webmanifest">
    <meta name="theme-color" content="#ff7a00">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Review your order at Magic Menu - Nigeria's premier food delivery service. Modify quantities, apply discounts, and proceed to secure checkout.">
    <meta name="keywords" content="food delivery, order review, shopping cart, Nigerian cuisine, Magic Menu">
    <meta name="author" content="Magic Menu">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Your Order - Magic Menu">
    <meta property="og:description" content="Review and modify your food order before checkout">
    <meta property="og:image" content="assets/images/og-cart.jpg">
    <meta property="og:url" content="https://magicmenu.ng/cart">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Your Order - Magic Menu">
    <meta name="twitter:description" content="Review and modify your food order before checkout">
    <meta name="twitter:image" content="assets/images/twitter-cart.jpg">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Shopping Cart - Magic Menu",
        "description": "Review your food order and proceed to checkout",
        "url": "https://magicmenu.ng/cart",
        "isPartOf": {
            "@type": "WebSite",
            "name": "Magic Menu",
            "url": "https://magicmenu.ng"
        }
    }
    </script>
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="assets/css/styles.css" as="style">
    <link rel="preload" href="assets/css/modern-cart.css" as="style">
    <link rel="preload" href="assets/scripts/modern-cart.js" as="script">
</head>
<body class="cart-page">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header -->
    <header class="header" role="banner">
        <div class="container header-content">
            <a href="index.html" class="logo" aria-label="Magic Menu - Go to homepage">
                <img src="assets/images/logo.png" alt="Magic Menu Logo" width="40" height="40">
                <span class="logo-text">Magic Menu</span>
            </a>
            
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="menu.html" class="nav-link">Menu</a></li>
                    <li><a href="modern-cart.html" class="nav-link active" aria-current="page">Order</a></li>
                    <li><a href="account.html" class="nav-link">Account</a></li>
                    <li><a href="contact.html" class="nav-link">Contact</a></li>
                </ul>
            </nav>
            
            <div class="cart-icon-wrapper">
                <button class="cart-icon" aria-label="View cart" aria-describedby="cart-count">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span id="cart-count" class="cart-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Page Header -->
            <header class="page-header">
                <h1 class="page-title">Your Order</h1>
                <p class="page-subtitle">Review your items and proceed to checkout</p>
            </header>

            <!-- Progress Indicator -->
            <nav class="checkout-progress" role="navigation" aria-label="Checkout progress">
                <ol class="progress-steps">
                    <li class="progress-step active" aria-current="step">
                        <div class="step-indicator">
                            <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                        </div>
                        <span class="step-label">Review Order</span>
                    </li>
                    <li class="progress-step">
                        <div class="step-indicator">
                            <i class="fas fa-credit-card" aria-hidden="true"></i>
                        </div>
                        <span class="step-label">Payment</span>
                    </li>
                    <li class="progress-step">
                        <div class="step-indicator">
                            <i class="fas fa-check-circle" aria-hidden="true"></i>
                        </div>
                        <span class="step-label">Confirmation</span>
                    </li>
                </ol>
            </nav>

            <!-- Cart Content -->
            <div class="cart-layout">
                <!-- Cart Items Section -->
                <section class="cart-items-section" aria-labelledby="cart-items-heading">
                    <h2 id="cart-items-heading" class="section-heading">
                        <i class="fas fa-utensils" aria-hidden="true"></i>
                        Your Items
                    </h2>
                    
                    <!-- Loading State -->
                    <div class="loading-state" aria-hidden="true">
                        <div class="loading-spinner"></div>
                        <p>Loading your cart...</p>
                    </div>
                    
                    <!-- Empty Cart State -->
                    <div class="empty-cart-state" aria-hidden="true">
                        <div class="empty-cart-illustration">
                            <i class="fas fa-shopping-basket" aria-hidden="true"></i>
                        </div>
                        <h3>Your cart is empty</h3>
                        <p>Discover our delicious menu and add some items to get started!</p>
                        <a href="menu.html" class="btn btn-primary">
                            <i class="fas fa-utensils" aria-hidden="true"></i>
                            Browse Menu
                        </a>
                    </div>
                    
                    <!-- Cart Items List -->
                    <div class="cart-items-list" role="list" aria-live="polite">
                        <!-- Items will be dynamically inserted here -->
                    </div>
                    
                    <!-- Cart Actions -->
                    <div class="cart-actions">
                        <button class="btn btn-secondary clear-cart-btn" type="button">
                            <i class="fas fa-trash-alt" aria-hidden="true"></i>
                            Clear Cart
                        </button>
                        <a href="menu.html" class="btn btn-outline">
                            <i class="fas fa-plus" aria-hidden="true"></i>
                            Add More Items
                        </a>
                    </div>
                </section>

                <!-- Order Summary Section -->
                <aside class="order-summary-section" aria-labelledby="order-summary-heading">
                    <div class="summary-card">
                        <h2 id="order-summary-heading" class="summary-heading">
                            <i class="fas fa-receipt" aria-hidden="true"></i>
                            Order Summary
                        </h2>
                        
                        <div class="summary-content">
                            <div class="summary-line">
                                <span>Subtotal</span>
                                <span class="summary-subtotal">₦0.00</span>
                            </div>
                            <div class="summary-line">
                                <span>Tax (7.5%)</span>
                                <span class="summary-tax">₦0.00</span>
                            </div>
                            <div class="summary-line">
                                <span>Delivery Fee</span>
                                <span class="summary-delivery">₦500.00</span>
                            </div>
                            <div class="summary-divider"></div>
                            <div class="summary-line total">
                                <span>Total</span>
                                <span class="summary-total">₦500.00</span>
                            </div>
                        </div>
                        
                        <!-- Promo Code Section -->
                        <div class="promo-section">
                            <details class="promo-details">
                                <summary class="promo-toggle">
                                    <i class="fas fa-tag" aria-hidden="true"></i>
                                    Have a promo code?
                                </summary>
                                <form class="promo-form" novalidate>
                                    <div class="input-group">
                                        <input type="text" 
                                               id="promo-code" 
                                               name="promoCode" 
                                               class="promo-input" 
                                               placeholder="Enter promo code"
                                               aria-describedby="promo-error">
                                        <button type="submit" class="btn btn-secondary">Apply</button>
                                    </div>
                                    <div id="promo-error" class="error-message" role="alert" aria-live="polite"></div>
                                </form>
                            </details>
                        </div>
                        
                        <!-- Checkout Button -->
                        <button class="checkout-btn btn btn-primary btn-large" 
                                type="button" 
                                disabled
                                aria-describedby="checkout-error">
                            <i class="fas fa-lock" aria-hidden="true"></i>
                            Proceed to Checkout
                        </button>
                        
                        <div id="checkout-error" class="error-message" role="alert" aria-live="polite"></div>
                        
                        <!-- Security Badge -->
                        <div class="security-badge">
                            <i class="fas fa-shield-alt" aria-hidden="true"></i>
                            <span>Secure checkout with SSL encryption</span>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <!-- Cart Item Template -->
    <template id="cart-item-template">
        <article class="cart-item" role="listitem" tabindex="0">
            <div class="item-image-container">
                <img src="" alt="" class="item-image" loading="lazy" width="120" height="120">
                <button class="remove-item-btn" 
                        type="button"
                        aria-label="Remove item from cart" 
                        title="Remove this item">
                    <i class="fas fa-times" aria-hidden="true"></i>
                </button>
            </div>
            
            <div class="item-details">
                <h3 class="item-title"></h3>
                <p class="item-description"></p>
                <div class="item-price-container">
                    <span class="item-price"></span>
                    <span class="item-total-price"></span>
                </div>
                
                <div class="item-controls">
                    <div class="quantity-controls" role="group" aria-label="Quantity controls">
                        <button class="quantity-btn decrease" 
                                type="button"
                                aria-label="Decrease quantity" 
                                title="Decrease quantity">
                            <i class="fas fa-minus" aria-hidden="true"></i>
                        </button>
                        <input type="number"
                               class="quantity-input"
                               value="1"
                               min="1"
                               max="99"
                               aria-label="Item quantity"
                               title="Item quantity">
                        <button class="quantity-btn increase" 
                                type="button"
                                aria-label="Increase quantity" 
                                title="Increase quantity">
                            <i class="fas fa-plus" aria-hidden="true"></i>
                        </button>
                    </div>
                    
                    <button class="save-for-later-btn" 
                            type="button"
                            title="Save for later">
                        <i class="fas fa-heart" aria-hidden="true"></i>
                        Save for later
                    </button>
                </div>
            </div>
        </article>
    </template>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container" aria-live="polite" aria-atomic="true"></div>

    <!-- Scripts -->
    <script type="module" src="assets/scripts/modern-cart.js"></script>
    <script>
        // Initialize AOS if available
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 600,
                easing: 'ease-out-cubic',
                once: true,
                offset: 50
            });
        }
    </script>
</body>
</html>
