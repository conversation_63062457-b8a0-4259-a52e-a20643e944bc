<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f7fa;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            text-align: center;
            margin-bottom: 1.5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        button {
            width: 100%;
            padding: 0.75rem;
            background: #ff7a00;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin-top: 1rem;
        }
        button:hover {
            background: #e66d00;
        }
        .error {
            color: red;
            margin-top: 1rem;
            text-align: center;
        }
        .success {
            color: green;
            margin-top: 1rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>Admin Login</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <div id="message"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            
            const loginForm = document.getElementById('loginForm');
            const messageDiv = document.getElementById('message');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('Form submitted');
                
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                console.log(`Login attempt: ${email}`);
                
                if (email === '<EMAIL>' && password === 'admin123') {
                    messageDiv.textContent = 'Login successful!';
                    messageDiv.className = 'success';
                    
                    // Redirect to admin dashboard after successful login
                    setTimeout(() => {
                        window.location.href = 'admin.html';
                        // Also store auth token
                        localStorage.setItem('adminAuthToken', 'dummy-token');
                    }, 1000);
                } else {
                    messageDiv.textContent = 'Invalid credentials. Please try again.';
                    messageDiv.className = 'error';
                }
            });
        });
    </script>
</body>
</html>
