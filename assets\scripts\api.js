import { ApiClient } from './utils/ApiClient.js';
import { ApiRetry } from './utils/ApiRetry.js';
import { ApiErrorHandler } from './utils/ApiErrorHandler.js';

class Api {
    constructor() {
        this.client = new ApiClient('https://api.magicmenu.com');
    }

    // Menu endpoints
    async getMenuItems() {
        return ApiRetry.retry(() => this.client.get('/api/menu'));
    }

    async getMenuItem(id) {
        return ApiRetry.retry(() => this.client.get(`/api/menu/${id}`));
    }

    // Cart endpoints
    async getCart() {
        return this.client.get('/api/cart');
    }

    async addToCart(itemId, quantity = 1) {
        return this.client.post('/api/cart/add', { itemId, quantity });
    }

    async updateCartItem(itemId, quantity) {
        return this.client.put('/api/cart/update', { itemId, quantity });
    }

    async removeFromCart(itemId) {
        return this.client.delete(`/api/cart/${itemId}`);
    }

    // Order endpoints
    async createOrder(orderData) {
        return this.client.post('/api/orders', orderData);
    }

    async getOrder(orderId) {
        return this.client.get(`/api/orders/${orderId}`);
    }

    // User endpoints
    async login(credentials) {
        return this.client.post('/api/auth/login', credentials);
    }

    async logout() {
        return this.client.post('/api/auth/logout');
    }

    async register(userData) {
        return this.client.post('/api/auth/register', userData);
    }

    // Error handling wrapper
    async request(operation, context = '') {
        try {
            return await operation();
        } catch (error) {
            const message = ApiErrorHandler.handleError(error, context);
            throw new Error(message);
        }
    }
}

// Export both the default instance and named export
const api = new Api();
export { api };
export default api;
