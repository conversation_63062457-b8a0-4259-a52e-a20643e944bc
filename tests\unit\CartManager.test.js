import { CartManager } from '../../assets/scripts/components';
import { CURRENCY_CONFIG } from '../../assets/scripts/config/currency';

describe('CartManager', () => {
    let cartManager;

    beforeEach(() => {
        cartManager = new CartManager();
        localStorage.clear();
    });

    describe('calculateTotals', () => {
        it('should correctly calculate cart totals', async () => {
            const items = [
                { price: 5000, quantity: 2 },
                { price: 3000, quantity: 1 }
            ];

            const totals = await cartManager.calculateTotals(items);

            expect(totals.subtotal).toBe(13000);
            expect(totals.tax).toBe(13000 * CURRENCY_CONFIG.vatRate);
            expect(totals.deliveryFee).toBe(CURRENCY_CONFIG.deliveryFee);
            expect(totals.total).toBe(
                13000 + (13000 * CURRENCY_CONFIG.vatRate) + CURRENCY_CONFIG.deliveryFee
            );
        });

        it('should handle invalid prices', async () => {
            const items = [{ price: 'invalid', quantity: 1 }];

            await expect(cartManager.calculateTotals(items)).rejects.toThrow();
        });
    });

    describe('validateQuantity', () => {
        it('should enforce minimum and maximum quantities', () => {
            expect(CartManager.validateQuantity(0)).toBe(1);
            expect(CartManager.validateQuantity(100, 99)).toBe(99);
            expect(CartManager.validateQuantity(5)).toBe(5);
        });
    });
});