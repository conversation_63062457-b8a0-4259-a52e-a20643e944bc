/**
 * Modern Contact Page Manager
 * Handles contact form validation, submission, and user experience
 * with modern ES6+ features and enhanced accessibility
 */

import { ModernErrorHandler } from './utils/ModernErrorHandler.js';
import { ModernUtils } from './utils/ModernUtils.js';
import Analytics from './services/Analytics.js';

class ModernContactManager {
    constructor() {
        this.elements = {};
        this.state = {
            isLoading: false,
            formData: {},
            validationErrors: {},
            isFormValid: false
        };

        this.validators = {
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            phone: /^(\+234|0)[789][01]\d{8}$|^(\+1|1)?[2-9]\d{2}[2-9]\d{2}\d{4}$/,
            required: /\S+/,
            minLength: (value, min) => value.length >= min
        };

        this.errorHandler = new ModernErrorHandler();
        this.analytics = new Analytics();

        this.init();
    }

    /**
     * Initialize the contact manager
     */
    init() {
        this.cacheElements();
        this.bindEvents();
        this.initializeFormValidation();
        this.initializeAnimations();
        this.setupAccessibility();
    }

    /**
     * Cache DOM elements
     */
    cacheElements() {
        this.elements = {
            form: document.getElementById('contactForm'),
            submitBtn: document.querySelector('.submit-btn'),
            btnText: document.querySelector('.submit-btn .btn-text'),
            btnIcon: document.querySelector('.submit-btn .btn-icon'),
            formInputs: document.querySelectorAll('.form-input'),
            toastContainer: this.createToastContainer(),

            // Form fields
            nameField: document.getElementById('name'),
            emailField: document.getElementById('email'),
            messageField: document.getElementById('message')
        };
    }

    /**
     * Create toast container for notifications
     */
    createToastContainer() {
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container';
            container.setAttribute('aria-live', 'polite');
            container.setAttribute('aria-atomic', 'true');
            document.body.appendChild(container);
        }
        return container;
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Form submission
        if (this.elements.form) {
            this.elements.form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }

        // Real-time validation
        this.elements.formInputs.forEach(input => {
            input.addEventListener('input', this.handleInputChange.bind(this));
            input.addEventListener('blur', this.handleInputBlur.bind(this));
            input.addEventListener('focus', this.handleInputFocus.bind(this));
        });

        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));

        // Window events
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
        window.addEventListener('online', () => this.showToast('Connection restored', 'success'));
        window.addEventListener('offline', () => this.showToast('You are offline', 'warning'));
    }

    /**
     * Initialize form validation
     */
    initializeFormValidation() {
        // Add required indicators
        const requiredFields = this.elements.form?.querySelectorAll('[required]');
        requiredFields?.forEach(field => {
            const label = document.querySelector(`label[for="${field.id}"]`);
            if (label && !label.classList.contains('required')) {
                label.classList.add('required');
            }
        });

        // Initial validation state
        this.updateSubmitButton();
    }

    /**
     * Initialize animations
     */
    initializeAnimations() {
        // Initialize AOS if available
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                once: true,
                offset: 100,
                easing: 'ease-out-cubic'
            });
        }

        // Stagger animation for info cards
        const infoCards = document.querySelectorAll('.info-card');
        infoCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.style.animation = 'fadeInUp 0.6s ease-out forwards';
        });
    }

    /**
     * Setup accessibility features
     */
    setupAccessibility() {
        // Add ARIA labels and descriptions
        if (this.elements.form) {
            this.elements.form.setAttribute('novalidate', 'true');
            this.elements.form.setAttribute('aria-label', 'Contact form');
        }

        // Add live region for form status
        const statusRegion = document.createElement('div');
        statusRegion.setAttribute('aria-live', 'polite');
        statusRegion.setAttribute('aria-atomic', 'true');
        statusRegion.className = 'sr-only';
        statusRegion.id = 'form-status';
        document.body.appendChild(statusRegion);

        // Enhance submit button accessibility
        if (this.elements.submitBtn) {
            this.elements.submitBtn.setAttribute('aria-describedby', 'form-status');
        }
    }

    /**
     * Handle input changes for real-time validation
     */
    handleInputChange(e) {
        const field = e.target;
        const fieldName = field.name || field.id;

        // Update form data
        this.state.formData[fieldName] = field.value;

        // Clear previous error state
        this.clearFieldError(field);

        // Validate if field has content or was previously validated
        if (field.value.trim() || this.state.validationErrors[fieldName]) {
            this.validateField(field);
        }

        // Update submit button state
        this.updateSubmitButton();
    }

    /**
     * Handle input blur for validation
     */
    handleInputBlur(e) {
        const field = e.target;
        this.validateField(field);
        this.updateSubmitButton();
    }

    /**
     * Handle input focus
     */
    handleInputFocus(e) {
        const field = e.target;
        this.clearFieldError(field);
    }

    /**
     * Validate individual field
     */
    validateField(field) {
        const fieldName = field.name || field.id;
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (field.hasAttribute('required') && !this.validators.required.test(value)) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} is required`;
        }

        // Specific field validations
        if (value && isValid) {
            switch (fieldName) {
                case 'email':
                    if (!this.validators.email.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                    break;

                case 'name':
                    if (!this.validators.minLength(value, 2)) {
                        isValid = false;
                        errorMessage = 'Name must be at least 2 characters long';
                    }
                    break;

                case 'message':
                    if (!this.validators.minLength(value, 10)) {
                        isValid = false;
                        errorMessage = 'Message must be at least 10 characters long';
                    }
                    break;
            }
        }

        // Update field state
        this.updateFieldState(field, isValid, errorMessage);

        // Update validation state
        this.state.validationErrors[fieldName] = isValid ? null : errorMessage;

        return isValid;
    }

    /**
     * Get field label text
     */
    getFieldLabel(field) {
        const label = document.querySelector(`label[for="${field.id}"]`);
        return label ? label.textContent.replace(' *', '') : field.name || field.id;
    }

    /**
     * Update field visual state
     */
    updateFieldState(field, isValid, errorMessage) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup?.querySelector('.error-message');

        if (!formGroup) return;

        // Remove previous states
        formGroup.classList.remove('error', 'success');

        if (!isValid && errorMessage) {
            // Show error state
            formGroup.classList.add('error');
            if (errorElement) {
                errorElement.textContent = errorMessage;
                errorElement.classList.add('show');
            }
        } else if (field.value.trim() && isValid) {
            // Show success state
            formGroup.classList.add('success');
            if (errorElement) {
                errorElement.classList.remove('show');
            }
        }
    }

    /**
     * Clear field error state
     */
    clearFieldError(field) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup?.querySelector('.error-message');

        if (formGroup) {
            formGroup.classList.remove('error');
            if (errorElement) {
                errorElement.classList.remove('show');
            }
        }
    }

    /**
     * Update submit button state
     */
    updateSubmitButton() {
        if (!this.elements.submitBtn) return;

        const requiredFields = this.elements.form?.querySelectorAll('[required]');
        let allValid = true;

        requiredFields?.forEach(field => {
            const fieldName = field.name || field.id;
            const hasError = this.state.validationErrors[fieldName];
            const hasValue = field.value.trim();

            if (!hasValue || hasError) {
                allValid = false;
            }
        });

        this.state.isFormValid = allValid;
        this.elements.submitBtn.disabled = !allValid || this.state.isLoading;

        // Update button appearance
        if (allValid && !this.state.isLoading) {
            this.elements.submitBtn.classList.add('ready');
        } else {
            this.elements.submitBtn.classList.remove('ready');
        }
    }

    /**
     * Handle form submission
     */
    async handleFormSubmit(e) {
        e.preventDefault();

        if (this.state.isLoading || !this.state.isFormValid) return;

        // Final validation
        const formInputs = this.elements.form.querySelectorAll('input[required], textarea[required]');
        let isFormValid = true;

        formInputs.forEach(input => {
            const isFieldValid = this.validateField(input);
            if (!isFieldValid) isFormValid = false;
        });

        if (!isFormValid) {
            this.showToast('Please fix the errors in the form', 'error');
            this.focusFirstError();
            return;
        }

        // Track form submission attempt
        this.trackEvent('contact_form_submit_attempt', {
            form_type: 'contact',
            page_url: window.location.href
        });

        try {
            await this.submitForm();
        } catch (error) {
            this.errorHandler.handleError(error);
            this.showToast('Failed to send message. Please try again.', 'error');
        }
    }

    /**
     * Submit form data
     */
    async submitForm() {
        this.setLoadingState(true);

        try {
            // Prepare form data
            const formData = new FormData(this.elements.form);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                message: formData.get('message'),
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                referrer: document.referrer
            };

            // Simulate API call (replace with actual endpoint)
            const response = await this.mockApiCall(data);

            if (response.success) {
                // Success handling
                this.handleSubmissionSuccess();
                this.trackEvent('contact_form_submit_success', {
                    form_type: 'contact',
                    response_time: response.responseTime
                });
            } else {
                throw new Error(response.message || 'Submission failed');
            }

        } catch (error) {
            this.trackEvent('contact_form_submit_error', {
                form_type: 'contact',
                error_message: error.message
            });
            throw error;
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * Mock API call for demonstration
     * Replace with actual API endpoint
     */
    async mockApiCall(data) {
        const startTime = Date.now();

        // Simulate network delay
        await ModernUtils.delay(1500);

        // Simulate occasional failures for testing
        if (Math.random() < 0.1) {
            throw new Error('Network error occurred');
        }

        return {
            success: true,
            message: 'Message sent successfully',
            responseTime: Date.now() - startTime,
            id: ModernUtils.generateId()
        };
    }

    /**
     * Handle successful form submission
     */
    handleSubmissionSuccess() {
        // Show success message
        this.showToast('Message sent successfully!', 'success', 'Thank you for contacting us. We\'ll get back to you soon.');

        // Reset form
        this.elements.form.reset();
        this.state.formData = {};
        this.state.validationErrors = {};

        // Clear all field states
        this.elements.formInputs.forEach(input => {
            this.clearFieldError(input);
            const formGroup = input.closest('.form-group');
            if (formGroup) {
                formGroup.classList.remove('success', 'error');
            }
        });

        // Update submit button
        this.updateSubmitButton();

        // Update status for screen readers
        this.updateFormStatus('Message sent successfully');

        // Focus on first field for better UX
        if (this.elements.nameField) {
            this.elements.nameField.focus();
        }
    }

    /**
     * Set loading state
     */
    setLoadingState(isLoading) {
        this.state.isLoading = isLoading;

        if (!this.elements.submitBtn) return;

        if (isLoading) {
            this.elements.submitBtn.disabled = true;
            this.elements.submitBtn.classList.add('loading');

            if (this.elements.btnText) {
                this.elements.btnText.textContent = 'Sending...';
            }

            if (this.elements.btnIcon) {
                this.elements.btnIcon.className = 'btn-icon fas fa-spinner';
            }
        } else {
            this.elements.submitBtn.disabled = !this.state.isFormValid;
            this.elements.submitBtn.classList.remove('loading');

            if (this.elements.btnText) {
                this.elements.btnText.textContent = 'Send Message';
            }

            if (this.elements.btnIcon) {
                this.elements.btnIcon.className = 'btn-icon fas fa-paper-plane';
            }
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', title = null) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <i class="toast-icon ${iconMap[type]}"></i>
            <div class="toast-content">
                ${title ? `<div class="toast-title">${title}</div>` : ''}
                <p class="toast-message">${message}</p>
            </div>
            <button class="toast-close" aria-label="Close notification">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add close functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.removeToast(toast));

        // Add to container
        this.elements.toastContainer.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => this.removeToast(toast), 5000);

        return toast;
    }

    /**
     * Remove toast notification
     */
    removeToast(toast) {
        if (!toast || !toast.parentNode) return;

        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    /**
     * Focus first error field
     */
    focusFirstError() {
        const errorField = this.elements.form?.querySelector('.form-group.error .form-input');
        if (errorField) {
            errorField.focus();
            errorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    /**
     * Update form status for screen readers
     */
    updateFormStatus(message) {
        const statusElement = document.getElementById('form-status');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    /**
     * Handle keyboard navigation
     */
    handleKeyboardNavigation(e) {
        // Escape key closes toasts
        if (e.key === 'Escape') {
            const toasts = this.elements.toastContainer.querySelectorAll('.toast');
            toasts.forEach(toast => this.removeToast(toast));
        }

        // Enter key on submit button
        if (e.key === 'Enter' && e.target === this.elements.submitBtn) {
            e.preventDefault();
            this.elements.submitBtn.click();
        }
    }

    /**
     * Handle before unload (warn about unsaved changes)
     */
    handleBeforeUnload(e) {
        const hasUnsavedChanges = Object.values(this.state.formData).some(value => value && value.trim());

        if (hasUnsavedChanges && !this.state.isLoading) {
            e.preventDefault();
            e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            return e.returnValue;
        }
    }

    /**
     * Track analytics events
     */
    trackEvent(eventName, properties = {}) {
        try {
            if (this.analytics && this.analytics.consent) {
                this.analytics.track(eventName, {
                    ...properties,
                    timestamp: new Date().toISOString(),
                    page_url: window.location.href
                });
            }
        } catch (error) {
            console.warn('Analytics tracking failed:', error);
        }
    }

    /**
     * Get current form data
     */
    getFormData() {
        return { ...this.state.formData };
    }

    /**
     * Check if form is valid
     */
    isFormValid() {
        return this.state.isFormValid;
    }

    /**
     * Reset form to initial state
     */
    resetForm() {
        if (this.elements.form) {
            this.elements.form.reset();
            this.state.formData = {};
            this.state.validationErrors = {};

            this.elements.formInputs.forEach(input => {
                this.clearFieldError(input);
                const formGroup = input.closest('.form-group');
                if (formGroup) {
                    formGroup.classList.remove('success', 'error');
                }
            });

            this.updateSubmitButton();
        }
    }

    /**
     * Destroy the contact manager
     */
    destroy() {
        // Remove event listeners
        if (this.elements.form) {
            this.elements.form.removeEventListener('submit', this.handleFormSubmit);
        }

        this.elements.formInputs.forEach(input => {
            input.removeEventListener('input', this.handleInputChange);
            input.removeEventListener('blur', this.handleInputBlur);
            input.removeEventListener('focus', this.handleInputFocus);
        });

        document.removeEventListener('keydown', this.handleKeyboardNavigation);
        window.removeEventListener('beforeunload', this.handleBeforeUnload);

        // Clear toasts
        const toasts = this.elements.toastContainer.querySelectorAll('.toast');
        toasts.forEach(toast => this.removeToast(toast));
    }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modernContactManager = new ModernContactManager();
});

// Smooth scroll for anchor links
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Export for manual initialization
export default ModernContactManager;
