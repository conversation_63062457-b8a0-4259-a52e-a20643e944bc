export const mockApiResponses = {
    successfulCheckout: {
        status: 'success',
        orderId: '12345',
        message: 'Order placed successfully'
    },
    failedCheckout: {
        status: 'error',
        message: 'Unable to process payment'
    }
};

export const setupApiMocks = () => {
    global.fetch = jest.fn((url) => {
        if (url.includes('/api/checkout')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve(mockApiResponses.successfulCheckout)
            });
        }
        return Promise.reject(new Error('Not found'));
    });
};