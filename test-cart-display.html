<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Display Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .test-pass {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-fail {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .test-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #test-results {
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Cart Page Display Test</h1>
        <p>This test verifies that the cart page displays correctly and content doesn't disappear after loading.</p>
        
        <div>
            <button onclick="runTests()">Run Tests</button>
            <button onclick="clearStorage()">Clear Cart Storage</button>
            <button onclick="addTestItems()">Add Test Items</button>
            <button onclick="openCartPage()">Open Cart Page</button>
            <button onclick="openCartPageNewWindow()">Open Cart in New Window</button>
        </div>
        
        <div id="test-results"></div>
        
        <h3>Cart Page Preview:</h3>
        <iframe id="cart-frame" src="cart.html"></iframe>
    </div>

    <script>
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearStorage() {
            localStorage.removeItem('cartItems');
            logResult('Cart storage cleared', 'info');
            refreshCartFrame();
        }

        function addTestItems() {
            const testItems = [
                {
                    id: 'test-1',
                    title: 'Test Pizza',
                    price: '₦2500.00',
                    quantity: 2,
                    image: 'assets/images/placeholder.svg'
                },
                {
                    id: 'test-2',
                    title: 'Test Burger',
                    price: '₦1800.00',
                    quantity: 1,
                    image: 'assets/images/placeholder.svg'
                }
            ];
            
            localStorage.setItem('cartItems', JSON.stringify(testItems));
            logResult('Test items added to cart', 'pass');
            refreshCartFrame();
        }

        function refreshCartFrame() {
            const frame = document.getElementById('cart-frame');
            frame.src = frame.src;
        }

        function openCartPage() {
            window.open('cart.html', '_blank');
        }

        function openCartPageNewWindow() {
            // Open cart page in a new window for better testing
            const cartWindow = window.open('cart.html', 'cartTest', 'width=1200,height=800');

            if (cartWindow) {
                logResult('Cart page opened in new window for manual testing', 'info');

                // Check if window loads successfully
                cartWindow.addEventListener('load', () => {
                    logResult('✓ Cart page loaded successfully in new window', 'pass');
                });

                cartWindow.addEventListener('error', () => {
                    logResult('✗ Error loading cart page in new window', 'fail');
                });
            } else {
                logResult('✗ Failed to open cart page (popup blocked?)', 'fail');
            }
        }

        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '';
            
            logResult('Starting cart page tests...', 'info');
            
            // Test 1: Check if localStorage is accessible
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                logResult('✓ localStorage is accessible', 'pass');
            } catch (error) {
                logResult('✗ localStorage is not accessible: ' + error.message, 'fail');
            }
            
            // Test 2: Check if cart page loads in iframe
            const frame = document.getElementById('cart-frame');
            frame.onload = function() {
                try {
                    const frameDoc = frame.contentDocument || frame.contentWindow.document;
                    const mainContent = frameDoc.querySelector('.main-content');
                    const cartHeader = frameDoc.querySelector('.cart-header');
                    const cartContentWrapper = frameDoc.querySelector('.cart-content-wrapper');
                    
                    if (mainContent) {
                        const styles = frameDoc.defaultView.getComputedStyle(mainContent);
                        if (styles.display !== 'none' && styles.visibility !== 'hidden') {
                            logResult('✓ Main content is visible', 'pass');
                        } else {
                            logResult('✗ Main content is hidden', 'fail');
                        }
                    } else {
                        logResult('✗ Main content element not found', 'fail');
                    }
                    
                    if (cartHeader) {
                        const styles = frameDoc.defaultView.getComputedStyle(cartHeader);
                        if (styles.display !== 'none' && styles.visibility !== 'hidden') {
                            logResult('✓ Cart header is visible', 'pass');
                        } else {
                            logResult('✗ Cart header is hidden', 'fail');
                        }
                    } else {
                        logResult('✗ Cart header element not found', 'fail');
                    }
                    
                    if (cartContentWrapper) {
                        const styles = frameDoc.defaultView.getComputedStyle(cartContentWrapper);
                        if (styles.display !== 'none' && styles.visibility !== 'hidden') {
                            logResult('✓ Cart content wrapper is visible', 'pass');
                        } else {
                            logResult('✗ Cart content wrapper is hidden', 'fail');
                        }
                    } else {
                        logResult('✗ Cart content wrapper element not found', 'fail');
                    }
                    
                } catch (error) {
                    logResult('✗ Error accessing iframe content: ' + error.message, 'fail');
                }
            };
            
            frame.onerror = function() {
                logResult('✗ Failed to load cart page in iframe', 'fail');
            };
            
            // Refresh the frame to trigger the tests
            refreshCartFrame();
            
            // Test 3: Check cart functionality after delay
            setTimeout(() => {
                logResult('Testing cart functionality after 3 seconds...', 'info');
                
                // Add test items and check if they persist
                addTestItems();
                
                setTimeout(() => {
                    const storedItems = localStorage.getItem('cartItems');
                    if (storedItems) {
                        try {
                            const items = JSON.parse(storedItems);
                            if (items.length > 0) {
                                logResult('✓ Cart items persist in storage', 'pass');
                            } else {
                                logResult('✗ No cart items found in storage', 'fail');
                            }
                        } catch (error) {
                            logResult('✗ Error parsing cart items: ' + error.message, 'fail');
                        }
                    } else {
                        logResult('✗ No cart data found in storage', 'fail');
                    }
                }, 1000);
                
            }, 3000);
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
