// Comprehensive test for menu functionality
// Run this in the browser console on the menu page

console.log('🧪 Starting Menu Functionality Tests...\n');

// Test 1: Check if all required elements exist
console.log('1️⃣ Testing Element Existence:');
const tests = {
    menuContent: document.querySelector('.menu-content'),
    quickViewButtons: document.querySelectorAll('.quick-view-btn'),
    addToCartButtons: document.querySelectorAll('.add-to-cart-btn'),
    cartIcon: document.getElementById('cartIcon'),
    cartCount: document.querySelector('.cart-count'),
    modal: document.getElementById('itemDetailModal'),
    modalTitle: document.getElementById('modalTitle'),
    modalImage: document.getElementById('modalImage'),
    modalPrice: document.getElementById('modalPrice'),
    modalAddToCart: document.getElementById('modalAddToCart')
};

Object.entries(tests).forEach(([name, element]) => {
    if (name.includes('Buttons')) {
        console.log(`   ${element.length > 0 ? '✅' : '❌'} ${name}: ${element.length} found`);
    } else {
        console.log(`   ${element ? '✅' : '❌'} ${name}: ${element ? 'Found' : 'Not found'}`);
    }
});

// Test 2: Test Quick View functionality
console.log('\n2️⃣ Testing Quick View:');
if (tests.quickViewButtons.length > 0) {
    const firstBtn = tests.quickViewButtons[0];
    const card = firstBtn.closest('.menu-item-card');
    
    if (card) {
        console.log('   📋 Card data:', {
            id: card.dataset.id,
            title: card.dataset.title,
            price: card.dataset.price
        });
        
        // Simulate click
        console.log('   🖱️ Simulating quick view click...');
        firstBtn.click();
        
        setTimeout(() => {
            if (tests.modal.style.display === 'block') {
                console.log('   ✅ Modal opened successfully');
                console.log('   📝 Modal content:', {
                    title: tests.modalTitle.textContent,
                    price: tests.modalPrice.textContent
                });
                
                // Close modal
                const closeBtn = tests.modal.querySelector('.close-modal');
                if (closeBtn) closeBtn.click();
            } else {
                console.log('   ❌ Modal did not open');
            }
        }, 100);
    }
} else {
    console.log('   ❌ No quick view buttons found');
}

// Test 3: Test Add to Cart functionality
console.log('\n3️⃣ Testing Add to Cart:');
setTimeout(() => {
    if (tests.addToCartButtons.length > 0) {
        const firstBtn = tests.addToCartButtons[0];
        const card = firstBtn.closest('.menu-item-card');
        
        if (card) {
            // Clear cart first
            localStorage.removeItem('cartItems');
            
            console.log('   🛒 Testing add to cart for:', card.dataset.title);
            
            // Simulate click
            firstBtn.click();
            
            setTimeout(() => {
                const cartItems = localStorage.getItem('cartItems');
                if (cartItems) {
                    const items = JSON.parse(cartItems);
                    console.log('   ✅ Item added to cart:', items);
                    console.log('   📊 Cart count should be:', items.reduce((sum, item) => sum + item.quantity, 0));
                    
                    // Check if UI updated
                    const displayedCount = tests.cartCount.textContent;
                    console.log('   🔢 Cart count in UI:', displayedCount);
                } else {
                    console.log('   ❌ Item was not added to cart');
                }
            }, 200);
        }
    } else {
        console.log('   ❌ No add to cart buttons found');
    }
}, 500);

// Test 4: Test Modal Add to Cart
console.log('\n4️⃣ Testing Modal Add to Cart:');
setTimeout(() => {
    if (tests.quickViewButtons.length > 0) {
        // Clear cart
        localStorage.removeItem('cartItems');
        
        // Open modal
        const firstBtn = tests.quickViewButtons[0];
        firstBtn.click();
        
        setTimeout(() => {
            if (tests.modal.style.display === 'block') {
                console.log('   🖱️ Modal opened, testing add to cart...');
                
                // Click modal add to cart
                tests.modalAddToCart.click();
                
                setTimeout(() => {
                    const cartItems = localStorage.getItem('cartItems');
                    if (cartItems) {
                        const items = JSON.parse(cartItems);
                        console.log('   ✅ Item added from modal:', items);
                    } else {
                        console.log('   ❌ Item was not added from modal');
                    }
                }, 200);
            }
        }, 100);
    }
}, 1000);

// Test 5: Test Navigation
console.log('\n5️⃣ Testing Navigation:');
const categoryLinks = document.querySelectorAll('.category-link');
console.log(`   📍 Found ${categoryLinks.length} category links`);

if (categoryLinks.length > 0) {
    const firstLink = categoryLinks[0];
    const href = firstLink.getAttribute('href');
    console.log(`   🔗 Testing navigation to: ${href}`);
    
    if (href && href.startsWith('#')) {
        const targetId = href.substring(1);
        const targetElement = document.getElementById(targetId);
        console.log(`   ${targetElement ? '✅' : '❌'} Target element ${targetId}: ${targetElement ? 'Found' : 'Not found'}`);
    }
}

// Test 6: Test Cart Icon
console.log('\n6️⃣ Testing Cart Icon:');
if (tests.cartIcon) {
    console.log('   🖱️ Testing cart icon click...');
    // We won't actually click it since it navigates away
    console.log('   ✅ Cart icon is clickable and should navigate to cart.html');
} else {
    console.log('   ❌ Cart icon not found');
}

console.log('\n🏁 Tests completed! Check the results above.');
