// Standalone menu functionality - no external dependencies
// This file provides basic menu functionality without complex imports

class SimpleAlert {
    static show(message, type = 'info', duration = 3000) {
        // Remove existing alert
        const existingAlert = document.querySelector('.simple-alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        const alert = document.createElement('div');
        alert.className = `simple-alert simple-alert-${type}`;
        alert.textContent = message;
        
        // Basic styles
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
            max-width: 300px;
        `;
        
        // Type-specific colors
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            info: '#17a2b8',
            warning: '#ffc107'
        };
        
        alert.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(alert);
        
        // Show animation
        setTimeout(() => {
            alert.style.opacity = '1';
        }, 10);
        
        // Auto remove
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 300);
        }, duration);
    }
}

class SimpleCartManager {
    static getCartItems() {
        try {
            const cartData = localStorage.getItem('cartItems');
            return cartData ? JSON.parse(cartData) : [];
        } catch (error) {
            console.error('Error reading cart from localStorage:', error);
            return [];
        }
    }
    
    static saveCartItems(items) {
        try {
            localStorage.setItem('cartItems', JSON.stringify(items));
        } catch (error) {
            console.error('Error saving cart to localStorage:', error);
        }
    }
    
    static addToCart(item) {
        try {
            // Validate item data
            if (!item || !item.title || !item.price) {
                throw new Error('Invalid item data');
            }

            // Parse price to remove currency symbol and convert to number
            let parsedPrice = item.price;
            if (typeof parsedPrice === 'string') {
                parsedPrice = parseFloat(parsedPrice.replace(/[^\d.-]/g, ''));
            }

            if (isNaN(parsedPrice) || parsedPrice <= 0) {
                throw new Error('Invalid price format');
            }

            // Get existing cart items
            const cartItems = this.getCartItems();

            // Check if item already exists in cart
            const existingItemIndex = cartItems.findIndex(cartItem => cartItem.id === item.id);

            if (existingItemIndex > -1) {
                // Update quantity if item exists
                cartItems[existingItemIndex].quantity += (item.quantity || 1);
            } else {
                // Add new item to cart
                cartItems.push({
                    id: item.id,
                    title: item.title,
                    description: item.description,
                    price: item.price,
                    numericPrice: parsedPrice,
                    image: item.image,
                    quantity: item.quantity || 1
                });
            }

            // Save to localStorage
            this.saveCartItems(cartItems);
            
            SimpleAlert.show(`${item.title} added to cart!`, 'success');
            
            // Update cart count
            this.updateCartCount();
            
            return true;
        } catch (error) {
            console.error('Error adding item to cart:', error);
            SimpleAlert.show('Failed to add item to cart. Please try again.', 'error');
            return false;
        }
    }
    
    static updateCartCount() {
        const cartItems = this.getCartItems();
        const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
        
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
            element.textContent = totalItems;
        });
    }
}

class SimpleMenuDetail {
    constructor() {
        this.currentItem = null;
        this.initializeModal();
    }
    
    initializeModal() {
        console.log('Initializing modal...');
        this.modal = document.getElementById('itemDetailModal');
        if (!this.modal) {
            console.error('Modal element not found');
            return;
        }
        console.log('Modal found:', this.modal);

        this.modalImage = document.getElementById('modalImage');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalDescription = document.getElementById('modalDescription');
        this.modalPrice = document.getElementById('modalPrice');
        this.modalCalories = document.getElementById('modalCalories');
        this.modalProtein = document.getElementById('modalProtein');
        this.modalCarbs = document.getElementById('modalCarbs');
        this.modalIngredients = document.getElementById('modalIngredients');
        this.modalQuantity = document.getElementById('modalQuantity');
        this.modalAddToCart = document.getElementById('modalAddToCart');

        console.log('Modal elements found:', {
            image: !!this.modalImage,
            title: !!this.modalTitle,
            description: !!this.modalDescription,
            price: !!this.modalPrice,
            addToCart: !!this.modalAddToCart
        });

        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // Close modal
        const closeBtn = this.modal.querySelector('.close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeModal());
        }
        
        // Close on outside click
        window.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });
        
        // Quantity controls
        const decreaseBtn = this.modal.querySelector('.quantity-btn.decrease');
        const increaseBtn = this.modal.querySelector('.quantity-btn.increase');
        
        if (decreaseBtn) {
            decreaseBtn.addEventListener('click', () => {
                const currentVal = parseInt(this.modalQuantity.value);
                if (currentVal > 1) this.modalQuantity.value = currentVal - 1;
            });
        }
        
        if (increaseBtn) {
            increaseBtn.addEventListener('click', () => {
                const currentVal = parseInt(this.modalQuantity.value);
                if (currentVal < 10) this.modalQuantity.value = currentVal + 1;
            });
        }
        
        // Add to cart from modal
        if (this.modalAddToCart) {
            this.modalAddToCart.addEventListener('click', () => this.addToCart());
        }
    }
    
    openModal(itemData) {
        console.log('Opening modal with data:', itemData);
        this.currentItem = itemData;

        if (!this.modal) {
            console.error('Modal element not found!');
            return;
        }

        // Update modal content
        if (this.modalImage) {
            this.modalImage.src = itemData.image;
            this.modalImage.alt = itemData.title;
            console.log('Updated modal image');
        }
        if (this.modalTitle) {
            this.modalTitle.textContent = itemData.title;
            console.log('Updated modal title');
        }
        if (this.modalDescription) {
            this.modalDescription.textContent = itemData.description;
            console.log('Updated modal description');
        }
        if (this.modalPrice) {
            this.modalPrice.textContent = itemData.price;
            console.log('Updated modal price');
        }
        if (this.modalCalories) this.modalCalories.textContent = itemData.nutrition?.calories || 'N/A';
        if (this.modalProtein) this.modalProtein.textContent = itemData.nutrition?.protein || 'N/A';
        if (this.modalCarbs) this.modalCarbs.textContent = itemData.nutrition?.carbs || 'N/A';
        if (this.modalIngredients) {
            const ingredients = Array.isArray(itemData.ingredients) ? itemData.ingredients.join(', ') : itemData.ingredients;
            this.modalIngredients.textContent = ingredients || 'N/A';
        }

        // Reset quantity
        if (this.modalQuantity) this.modalQuantity.value = 1;

        // Show modal
        console.log('Showing modal...');
        this.modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        console.log('Modal should now be visible');
    }
    
    closeModal() {
        this.modal.style.display = 'none';
        document.body.style.overflow = '';
    }
    
    addToCart() {
        if (!this.currentItem) {
            SimpleAlert.show('No item selected', 'error');
            return;
        }
        
        const quantity = parseInt(this.modalQuantity.value) || 1;
        const item = {
            id: this.currentItem.id,
            title: this.currentItem.title,
            description: this.currentItem.description,
            price: this.currentItem.price,
            image: this.currentItem.image,
            quantity: quantity
        };
        
        if (SimpleCartManager.addToCart(item)) {
            this.closeModal();
        }
    }
}

// Initialize menu functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing standalone menu functionality...');
    
    // Initialize menu detail modal
    const menuDetail = new SimpleMenuDetail();
    
    // Initialize cart count
    SimpleCartManager.updateCartCount();
    
    // Quick view button handlers
    document.addEventListener('click', (e) => {
        const quickViewBtn = e.target.closest('.quick-view-btn');
        if (quickViewBtn) {
            console.log('Quick view button clicked!');
            e.preventDefault();

            const card = quickViewBtn.closest('.menu-item-card');
            if (!card) {
                console.error('Menu item card not found');
                return;
            }

            console.log('Card found:', card);
            console.log('Card dataset:', card.dataset);

            const itemData = {
                id: card.dataset.id || crypto.randomUUID(),
                image: card.querySelector('img')?.src || '',
                title: card.dataset.title || '',
                description: card.dataset.description || '',
                price: card.dataset.price || '',
                nutrition: {
                    calories: card.dataset.calories || 'N/A',
                    protein: card.dataset.protein || 'N/A',
                    carbs: card.dataset.carbs || 'N/A',
                },
                ingredients: card.dataset.ingredients ? card.dataset.ingredients.split(', ') : ['N/A'],
            };

            console.log('Item data:', itemData);
            menuDetail.openModal(itemData);
        }
        
        // Add to cart button handlers
        const addToCartBtn = e.target.closest('.add-to-cart-btn');
        if (addToCartBtn) {
            e.preventDefault();
            
            const card = addToCartBtn.closest('.menu-item-card');
            if (!card) {
                console.error('Menu item card not found');
                return;
            }
            
            const itemData = {
                id: card.dataset.id || crypto.randomUUID(),
                title: card.dataset.title || card.querySelector('.card-title')?.textContent,
                description: card.dataset.description || card.querySelector('.card-description')?.textContent,
                price: card.dataset.price || card.querySelector('.card-price')?.textContent,
                image: card.querySelector('img')?.src,
                quantity: 1
            };
            
            SimpleCartManager.addToCart(itemData);
        }
    });
    
    // Cart icon click handler
    const cartIcon = document.getElementById('cartIcon');
    if (cartIcon) {
        cartIcon.addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = 'cart.html';
        });
    }
    
    console.log('Standalone menu functionality initialized successfully');
});
