.cart-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out;
}

.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 100%;
    max-width: 400px;
    height: 100%;
    background: #fff;
    z-index: 1000;
    transition: right 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.cart-sidebar.active {
    right: 0;
}

.cart-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

.cart-sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.cart-sidebar-footer {
    padding: 1rem;
    border-top: 1px solid #eee;
}

.cart-empty-message {
    text-align: center;
    padding: 2rem;
    color: #666;
    display: none;
}

.cart-summary {
    margin-bottom: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.summary-row.total {
    font-weight: bold;
    font-size: 1.2em;
    border-top: 1px solid #eee;
    padding-top: 0.5rem;
}

.checkout-btn {
    width: 100%;
}

.cart-item {
    display: flex;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.cart-item:last-child {
    border-bottom: none;
}