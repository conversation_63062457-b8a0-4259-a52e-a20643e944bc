import { ErrorBoundary } from './ErrorBoundary.js';

export class ComponentErrorBoundary {
    constructor(component, options = {}) {
        this.component = component;
        this.options = {
            fallbackUI: null,
            onError: null,
            retryable: true,
            ...options
        };
    }

    async execute(method, ...args) {
        try {
            return await method.apply(this.component, args);
        } catch (error) {
            this.handleError(error);
            throw error; // Re-throw for upstream handling if needed
        }
    }

    handleError(error) {
        const componentName = this.component.constructor.name;
        
        if (this.options.onError) {
            this.options.onError(error, componentName);
        }

        ErrorBoundary.handleError(error, componentName, this.options.fallbackUI);
    }
}