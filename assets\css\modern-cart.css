/* Modern Cart Page Styles */

/* CSS Custom Properties for Cart */
:root {
    --cart-border-radius: 12px;
    --cart-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cart-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --cart-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --cart-animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Cart Page Layout */
.cart-page {
    background: linear-gradient(135deg, var(--background-color) 0%, #f1f5f9 100%);
    min-height: 100vh;
}

.main-content {
    padding: calc(var(--spacing-xl) + 65px) 0 var(--spacing-xl);
    min-height: calc(100vh - 200px);
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.page-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* Progress Indicator */
.checkout-progress {
    margin: var(--spacing-xl) 0;
    display: flex;
    justify-content: center;
}

.progress-steps {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    z-index: 2;
}

.progress-step::after {
    content: '';
    position: absolute;
    top: 25px;
    left: 50px;
    width: 60px;
    height: 2px;
    background: var(--border-color);
    z-index: 1;
}

.progress-step:last-child::after {
    display: none;
}

.progress-step.active::after {
    background: var(--primary-color);
}

.step-indicator {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 1.2rem;
    transition: var(--cart-transition);
    position: relative;
    z-index: 2;
}

.progress-step.active .step-indicator {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(255, 122, 0, 0.2);
}

.step-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-light);
    text-align: center;
}

.progress-step.active .step-label {
    color: var(--primary-color);
    font-weight: 600;
}

/* Cart Layout */
.cart-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-xl);
    align-items: start;
}

/* Cart Items Section */
.cart-items-section {
    background: var(--white);
    border-radius: var(--cart-border-radius);
    box-shadow: var(--cart-shadow);
    padding: var(--spacing-xl);
    transition: var(--cart-transition);
}

.cart-items-section:hover {
    box-shadow: var(--cart-shadow-hover);
}

.section-heading {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--background-color);
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) 0;
    gap: var(--spacing-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--background-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty Cart State */
.empty-cart-state {
    text-align: center;
    padding: var(--spacing-xl) 0;
}

.empty-cart-illustration {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    opacity: 0.7;
}

.empty-cart-state h3 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
}

.empty-cart-state p {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Cart Items List */
.cart-items-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Cart Item */
.cart-item {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--cart-border-radius);
    background: var(--white);
    transition: var(--cart-transition);
    position: relative;
    cursor: pointer;
}

.cart-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--cart-shadow);
    transform: translateY(-2px);
}

.cart-item:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.item-image-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--background-color);
}

.item-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--cart-transition);
}

.cart-item:hover .item-image {
    transform: scale(1.05);
}

.remove-item-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.95);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--cart-transition);
    backdrop-filter: blur(4px);
}

.remove-item-btn:hover {
    background: #ef4444;
    color: var(--white);
    transform: scale(1.1);
}

/* Item Details */
.item-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.item-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    line-height: 1.3;
}

.item-description {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.item-price-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-xs) 0;
}

.item-price {
    font-size: 0.9rem;
    color: var(--text-light);
}

.item-total-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Item Controls */
.item-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: var(--spacing-sm);
}

.quantity-controls {
    display: flex;
    align-items: center;
    background: var(--background-color);
    border-radius: var(--border-radius);
    padding: 4px;
    gap: 2px;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: calc(var(--border-radius) - 2px);
    transition: var(--cart-transition);
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-input {
    width: 50px;
    height: 32px;
    border: none;
    background: none;
    text-align: center;
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.quantity-input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
    border-radius: 4px;
}

.save-for-later-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 0.8rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: var(--border-radius);
    transition: var(--cart-transition);
}

.save-for-later-btn:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

/* Cart Actions */
.cart-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* Order Summary Section */
.order-summary-section {
    position: sticky;
    top: calc(65px + var(--spacing-lg));
}

.summary-card {
    background: var(--white);
    border-radius: var(--cart-border-radius);
    box-shadow: var(--cart-shadow);
    padding: var(--spacing-xl);
    transition: var(--cart-transition);
}

.summary-card:hover {
    box-shadow: var(--cart-shadow-hover);
}

.summary-heading {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--background-color);
}

.summary-content {
    margin-bottom: var(--spacing-lg);
}

.summary-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 0.95rem;
}

.summary-line.total {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    padding-top: var(--spacing-sm);
    border-top: 2px solid var(--border-color);
    margin-top: var(--spacing-md);
}

.summary-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-md) 0;
}

/* Promo Code Section */
.promo-section {
    margin-bottom: var(--spacing-lg);
}

.promo-details {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.promo-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-color);
    cursor: pointer;
    font-weight: 500;
    color: var(--text-color);
    transition: var(--cart-transition);
}

.promo-toggle:hover {
    background: var(--primary-color);
    color: var(--white);
}

.promo-form {
    padding: var(--spacing-md);
}

.input-group {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.promo-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: var(--cart-transition);
}

.promo-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 122, 0, 0.1);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--cart-transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--cart-shadow);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover:not(:disabled) {
    background: #34495e;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    font-weight: 600;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.checkout-btn {
    width: 100%;
    margin-bottom: var(--spacing-md);
}

/* Security Badge */
.security-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-light);
    text-align: center;
}

/* Error Messages */
.error-message {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: var(--spacing-xs);
    display: none;
}

.error-message.show {
    display: block;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: calc(65px + var(--spacing-md));
    right: var(--spacing-md);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--cart-shadow-hover);
    padding: var(--spacing-md);
    max-width: 300px;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    animation: slideInRight 0.3s var(--cart-animation-bounce);
}

.toast.success {
    border-left: 4px solid #10b981;
}

.toast.error {
    border-left: 4px solid #ef4444;
}

.toast.warning {
    border-left: 4px solid #f59e0b;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .cart-layout {
        grid-template-columns: 1fr 350px;
    }
}

@media (max-width: 768px) {
    .cart-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .order-summary-section {
        position: static;
        order: -1;
    }
    
    .progress-steps {
        gap: var(--spacing-md);
    }
    
    .progress-step::after {
        width: 40px;
        left: 40px;
    }
    
    .step-indicator {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .step-label {
        font-size: 0.8rem;
    }
    
    .cart-item {
        grid-template-columns: 100px 1fr;
    }
    
    .item-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .cart-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: calc(var(--spacing-lg) + 65px) 0 var(--spacing-lg);
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .cart-items-section,
    .summary-card {
        padding: var(--spacing-lg);
    }
    
    .progress-steps {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .progress-step::after {
        top: 40px;
        left: 50%;
        transform: translateX(-50%);
        width: 2px;
        height: 20px;
    }
    
    .cart-item {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .item-image-container {
        justify-self: center;
        width: 200px;
        height: 150px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .cart-item {
        border-width: 2px;
    }
    
    .btn {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
        border: 4px solid var(--primary-color);
    }
}
