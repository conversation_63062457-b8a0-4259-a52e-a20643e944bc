const { Pool } = require('pg');
const sql = require('sql-template-strings');

class DatabaseSecurity {
    constructor() {
        this.pool = new Pool({
            ssl: process.env.NODE_ENV === 'production',
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        });
    }

    async query(text, params) {
        const client = await this.pool.connect();
        try {
            // Use parameterized queries
            const result = await client.query(text, params);
            return result.rows;
        } finally {
            client.release();
        }
    }

    // Safe query builder for common operations
    async safeSelect(table, conditions, fields = ['*']) {
        const query = sql`
            SELECT ${fields.join(', ')}
            FROM ${table}
            WHERE ${Object.keys(conditions).map(key => 
                `${key} = ${conditions[key]}`).join(' AND ')}
        `;
        return this.query(query);
    }

    async safeInsert(table, data) {
        const fields = Object.keys(data);
        const values = Object.values(data);
        const query = sql`
            INSERT INTO ${table} (${fields.join(', ')})
            VALUES (${values.map((_, i) => `$${i + 1}`).join(', ')})
            RETURNING *
        `;
        return this.query(query, values);
    }
}

module.exports = new DatabaseSecurity();