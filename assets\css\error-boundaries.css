.error-boundary-fallback,
.error-boundary-default {
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius);
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
}

.error-boundary-default .error-content {
    text-align: center;
    padding: var(--spacing-lg);
}

.error-boundary-default i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.error-boundary-default h3 {
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
}

.error-boundary-default p {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.retry-button {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.retry-button:hover {
    background-color: var(--primary-dark);
}

.error-boundary-fallback.critical {
    background-color: #fff5f5;
    border-color: #feb2b2;
}

.error-toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    box-shadow: var(--shadow-md);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    z-index: 1000;
    max-width: 300px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}