<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Experience the magic of dining at Magic Menu. Fresh ingredients, exceptional service, and unforgettable flavors await you.">
    <meta property="og:title" content="Magic Menu - Experience Culinary Magic">
    <meta property="og:description" content="Join us at Magic Menu for an extraordinary dining experience with our carefully crafted menu and magical atmosphere.">
    <meta property="og:site_name" content="Magic Menu">
    <meta property="og:image" content="https://example.com/images/og-image.jpg">  <!-- Replace with your actual OG image -->
    <meta property="og:url" content="https://example.com">  <!-- Replace with your actual URL -->
    <link rel="canonical" href="https://example.com">  <!-- Replace with your actual canonical URL -->

    <!-- Preload critical resources -->
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/modern-base.css">
    <link rel="stylesheet" href="assets/css/modern-header.css">
    <link rel="preload" href="assets/css/styles.css" as="style">
    <link rel="preload" href="assets/css/modern-header.css" as="style">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="manifest" href="assets/images/site.webmanifest">

    <title>Magic Menu - Home</title>

    <!-- Structured Data -->
    <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Restaurant",
          "name": "Magic Menu",
          "image": "https://example.com/images/logo.jpg",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "123 Main Street",
            "addressLocality": "City",
            "addressRegion": "State",
            "postalCode": "12345"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": "40.7128",
            "longitude": "-74.0060"
          },
          "telephone": "+1-************",
          "openingHoursSpecification": {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": [
              "Monday",
              "Tuesday",
              "Wednesday",
              "Thursday",
              "Friday"
            ],
            "opens": "11:00",
            "closes": "22:00"
          },
        "priceRange" : "₦₦",
        "servesCuisine" : "Nigerian"
        }
        </script>
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Modern Header -->
    <header class="header" role="banner">
        <div class="header-content">
            <a href="index.html" class="logo" aria-label="Magic Menu - Go to homepage">
                <img src="assets/images/logo.png" alt="Magic Menu Logo" width="40" height="40">
                <span class="logo-text">Magic Menu</span>
            </a>

            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="menu.html" class="nav-link">Menu</a></li>
                    <li><a href="modern-cart.html" class="nav-link">Order</a></li>
                    <li><a href="about.html" class="nav-link">About</a></li>
                    <li><a href="contact.html" class="nav-link">Contact</a></li>
                    <li><a href="admin.html" class="nav-link">Admin</a></li>
                </ul>
            </nav>

            <div class="cart-icon-wrapper">
                <button class="cart-icon" aria-label="View cart" aria-describedby="cart-count">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span id="cart-count" class="cart-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section with Carousel -->
<section class="hero hero-home" id="main-content" role="main">
    <div class="hero-carousel">
        <div class="hero-slides">
            <div class="hero-slide">
                <div class="hero-background-layer">
                    <div class="hero-overlay"></div>
                    <img src="assets/images/hero-1.png" alt="Fresh ingredients and chef preparing food" class="hero-image" loading="eager" onerror="this.src='assets/images/fallback.jpg'; console.error('Failed to load:', this.src)">
                </div>
            </div>
            <div class="hero-slide">
                <div class="hero-background-layer">
                    <div class="hero-overlay"></div>
                    <img src="assets/images/hero-2.png" alt="Elegant restaurant interior" class="hero-image" loading="eager" onerror="this.src='assets/images/fallback.jpg'; console.error('Failed to load:', this.src)">
                </div>
            </div>
            <div class="hero-slide">
                <div class="hero-background-layer">
                    <div class="hero-overlay"></div>
                    <img src="assets/images/hero-3.png" alt="Signature dishes presentation" class="hero-image" loading="eager" onerror="this.src='assets/images/fallback.jpg'; console.error('Failed to load:', this.src)">
                </div>
            </div>
             <div class="hero-slide">
                <div class="hero-background-layer">
                    <div class="hero-overlay"></div>
                    <img src="assets/images/hero-4.png" alt="Outdoor dining area" class="hero-image" loading="eager" onerror="this.src='assets/images/fallback.jpg'; console.error('Failed to load:', this.src)">
                </div>
            </div>
            <div class="hero-slide">
                <div class="hero-background-layer">
                    <div class="hero-overlay"></div>
                    <img src="assets/images/hero-5.png" alt="Bar and lounge area" class="hero-image" loading="eager" onerror="this.src='assets/images/fallback.jpg'; console.error('Failed to load:', this.src)">
                </div>
            </div>
             <div class="hero-slide">
                <div class="hero-background-layer">
                    <div class="hero-overlay"></div>
                    <img src="assets/images/hero-6.png" alt="Special events and celebrations" class="hero-image" loading="eager" onerror="this.src='assets/images/fallback.jpg'; console.error('Failed to load:', this.src)">
                </div>
            </div>
           <div class="hero-slide">
                <div class="hero-background-layer">
                    <div class="hero-overlay"></div>
                    <img src="assets/images/hero-7.png" alt="Wine selection and pairing" class="hero-image" loading="eager" onerror="this.src='assets/images/fallback.jpg'; console.error('Failed to load:', this.src)">
                </div>
            </div>
            <div class="hero-slide">
                <div class="hero-background-layer">
                    <div class="hero-overlay"></div>
                    <img src="assets/images/hero-8.png" alt="Dessert presentation" class="hero-image" loading="eager" onerror="this.src='assets/images/fallback.jpg'; console.error('Failed to load:', this.src)">
                </div>
            </div>
        </div>

        <!-- Carousel Navigation -->
        <div class="carousel-nav">
            <button class="carousel-prev" aria-label="Previous slide"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-dots"></div>
            <button class="carousel-next" aria-label="Next slide"><i class="fas fa-chevron-right"></i></button>
        </div>
    </div>

    <!-- Hero Content -->
    <div class="container hero-content" data-aos="fade-up">
        <div class="hero-text">
            <h1 class="hero-heading">Delicious Food, <br>Delivered to You</h1>
            <p class="hero-subheading">Experience the best flavors in town</p>
            <a href="menu.html" class="btn btn-primary btn-animated">Order Now</a>
        </div>
        <div class="hero-interactive" data-aos="fade-up" data-aos-delay="200">
            <ul class="hero-interactive-list">
                <li>
                    <a href="menu.html" class="hero-interactive-icon">
                        <i class="fas fa-utensils"></i>
                        <span class="hero-interactive-label">Our Menu</span>
                    </a>
                </li>
                <li>
                    <a href="contact.html#location" class="hero-interactive-icon">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="hero-interactive-label">Find Us</span>
                    </a>
                </li>
                <li>
                    <a href="tel:+234-************" class="hero-interactive-icon">
                        <i class="fas fa-phone-alt"></i>
                        <span class="hero-interactive-label">Call Now</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</section>


    <!-- Main Content -->
    <main class="main-content">
        <!-- About Us Section -->
        <section class="about-section container section" id="about-section">
            <h2 class="section-heading" data-aos="fade-up">About Magic Menu</h2>
            <div class="about-content split-layout">
                <div class="about-image" data-aos="fade-right">
                    <img src="assets/images/restaurant-interior.jpg"
                         alt="Magic Menu restaurant interior"
                         loading="lazy">
                </div>
                <div class="about-text" data-aos="fade-left">
                    <p>Welcome to Magic Menu, where culinary artistry meets exceptional dining. Our passionate team crafts memorable experiences using the finest locally-sourced ingredients, making us a cherished destination for food lovers since our founding.</p>
                    <a href="about.html" class="btn btn-secondary">Learn More</a>
                </div>
            </div>
        </section>

        <!-- Menu Categories with Hover Effects -->
        <section class="categories-section container section" id="menu-categories">
            <h2 class="section-heading" data-aos="fade-up">Our Menu</h2>
            <div class="categories-grid">
                <!-- Local Delights Card -->
                <article class="card menu-card category-card" data-aos="fade-up" data-aos-delay="100">
                    <a href="menu.html#local" class="card-link">  <!-- Added a link to the entire card -->
                        <div class="card-image-wrapper">
                            <img src="assets/images/local-delights.jpg" alt="Local Nigerian Dishes" loading="lazy">
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Local Delights</h3>
                            <p class="card-description">Explore traditional Nigerian cuisine.</p>
                            <span class="btn btn-secondary">View Menu</span>
                        </div>
                    </a>
                </article>

                <!-- International Flavors Card -->
                <article class="card menu-card category-card" data-aos="fade-up" data-aos-delay="200">
                    <a href="menu.html#international" class="card-link">
                        <div class="card-image-wrapper">
                            <img src="assets/images/international-flavors.jpg" alt="International Dishes" loading="lazy">
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">International Flavors</h3>
                            <p class="card-description">Discover dishes from around the world.</p>
                            <span class="btn btn-secondary">View Menu</span>
                        </div>
                    </a>
                </article>
            </div>
        </section>

        <!-- Testimonials with Slider -->
        <section class="testimonials-section section" id="testimonials-section">
            <div class="container">
                <h2 class="section-heading" data-aos="fade-up">What Our Customers Say</h2>
                <div class="testimonials-slider" data-aos="fade-up">
                    <div class="testimonials-wrapper">
                        <div class="testimonial-card active">
                            <div class="testimonial-content">
                                <div class="testimonial-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="testimonial-text">"The food here is absolutely amazing! The flavors are authentic and the service is outstanding."</p>
                                <p class="testimonial-author">- John Smith</p>
                            </div>
                        </div>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <div class="testimonial-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="testimonial-text">"Best dining experience ever! The atmosphere is perfect and the menu selection is fantastic."</p>
                                <p class="testimonial-author">- Sarah Johnson</p>
                            </div>
                        </div>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <div class="testimonial-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="testimonial-text">"I can't get enough of their signature dishes. Everything is always fresh and delicious!"</p>
                                <p class="testimonial-author">- Mike Davis</p>
                            </div>
                        </div>
                    </div>
                    <div class="slider-controls">
                        <button class="slider-prev" aria-label="Previous testimonial">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="slider-next" aria-label="Next testimonial">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container footer-content">
            <div class="footer-section">
                <h4 class="footer-heading">Contact</h4>
                <p>123 Main Street<br>City, State 12345</p>
                <p>Phone: (*************</p>
                <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Hours</h4>
                <p>Mon-Fri: 11am - 10pm</p>
                <p>Sat-Sun: 12pm - 11pm</p>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Links</h4>
                <ul class="footer-links">
                    <li><a href="terms.html" class="footer-link">Terms & Conditions</a></li>
                    <li><a href="privacy.html" class="footer-link">Privacy Policy</a></li>
                    <li><a href="faq.html" class="footer-link">FAQ</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Follow Us</h4>
                <div class="social-links">
                    <a href="#" aria-label="facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" aria-label="twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" aria-label="instagram"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
        <div class="copyright">
            © 2025 Magic Menu. All rights reserved.
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
     <script>
        AOS.init({
          duration: 800,
          offset: 100,
          once: true
        });
    </script>
    <script src="assets/scripts/hero-slideshow.js" defer></script>
    <script src="assets/scripts/script.js" type="module" defer></script>
    <!-- IMPORTANT:  Include fallback.jpg in /assets/images/ -->
    <script>
        // Debug script to check image loading
        document.addEventListener('DOMContentLoaded', () => {
            const images = document.querySelectorAll('.hero-image');
            images.forEach((img, index) => {
                console.log(`Hero image ${index + 1} path:`, img.src);
                img.onerror = () => console.error(`Failed to load image ${index + 1}:`, img.src);
                img.onload = () => console.log(`Successfully loaded image ${index + 1}:`, img.src);
            });
        });
    </script>

    <!-- Modern Header Component -->
    <script type="module" src="assets/scripts/components/ModernHeader.js"></script>
</body>
</html>
















