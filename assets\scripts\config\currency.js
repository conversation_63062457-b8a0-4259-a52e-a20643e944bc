export const CURRENCY_CONFIG = {
    // Currency settings
    code: 'NGN',
    currency: 'NGN',
    symbol: '₦',
    locale: 'en-NG',

    // Pricing
    vatRate: 0.075, // 7.5% VAT
    deliveryFee: 500, // Base delivery fee in Naira (reduced for demo)
    minimumOrder: 1000, // Minimum order amount (reduced for demo)

    // Formatting options
    formatOptions: {
        style: 'currency',
        currency: 'NGN',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    },

    // Example prices for different categories
    defaultPrices: {
        starters: {
            min: 500,
            max: 2000
        },
        mains: {
            min: 1000,
            max: 5000
        },
        drinks: {
            min: 200,
            max: 1000
        },
        desserts: {
            min: 300,
            max: 1500
        }
    }
};

/**
 * Format price with currency
 */
export function formatPrice(amount) {
    try {
        // Handle string inputs
        if (typeof amount === 'string') {
            amount = parseFloat(amount.replace(/[^\d.-]/g, ''));
        }

        // Handle invalid amounts
        if (isNaN(amount) || amount < 0) {
            return `${CURRENCY_CONFIG.symbol}0.00`;
        }

        // Use Intl.NumberFormat for proper formatting
        return new Intl.NumberFormat(CURRENCY_CONFIG.locale, CURRENCY_CONFIG.formatOptions).format(amount);
    } catch (error) {
        // Fallback formatting
        return `${CURRENCY_CONFIG.symbol}${parseFloat(amount || 0).toFixed(2)}`;
    }
}

/**
 * Parse price from formatted string
 */
export function parsePrice(priceString) {
    if (typeof priceString !== 'string') {
        return parseFloat(priceString) || 0;
    }

    // Remove currency symbols and formatting
    const cleaned = priceString.replace(/[^\d.-]/g, '');
    return parseFloat(cleaned) || 0;
}

/**
 * Validate price
 */
export function validatePrice(price) {
    const numPrice = Number(price);
    if (isNaN(numPrice) || numPrice < 0) {
        throw new Error(`Invalid price: ${price}. Price must be a positive number in Naira.`);
    }
    return numPrice;
}

/**
 * Calculate tax amount
 */
export function calculateTax(amount) {
    return amount * CURRENCY_CONFIG.vatRate;
}

/**
 * Calculate total with tax and delivery
 */
export function calculateTotal(subtotal, includeDelivery = true) {
    const tax = calculateTax(subtotal);
    const delivery = includeDelivery ? CURRENCY_CONFIG.deliveryFee : 0;
    return subtotal + tax + delivery;
}

/**
 * Format currency for display in different contexts
 */
export function formatCurrencyDisplay(amount, context = 'default') {
    const formattedAmount = formatPrice(amount);

    switch (context) {
        case 'compact':
            // For small displays, use shorter format
            if (amount >= 1000000) {
                return `${CURRENCY_CONFIG.symbol}${(amount / 1000000).toFixed(1)}M`;
            } else if (amount >= 1000) {
                return `${CURRENCY_CONFIG.symbol}${(amount / 1000).toFixed(1)}K`;
            }
            return formattedAmount;

        case 'verbose':
            // For detailed displays
            return `${formattedAmount} ${CURRENCY_CONFIG.currency}`;

        default:
            return formattedAmount;
    }
}

/**
 * Check if amount meets minimum order requirement
 */
export function meetsMinimumOrder(amount) {
    return amount >= CURRENCY_CONFIG.minimumOrder;
}

/**
 * Get minimum order message
 */
export function getMinimumOrderMessage() {
    return `Minimum order amount is ${formatPrice(CURRENCY_CONFIG.minimumOrder)}`;
}

export default {
    CURRENCY_CONFIG,
    formatPrice,
    parsePrice,
    validatePrice,
    calculateTax,
    calculateTotal,
    formatCurrencyDisplay,
    meetsMinimumOrder,
    getMinimumOrderMessage
};