/* Modern Base Styles - Enhanced Foundation */

/* Enhanced CSS Custom Properties */
:root {
    /* Enhanced Color Palette */
    --primary-color: #ff7a00;
    --primary-dark: #e66d00;
    --primary-light: #ff9533;
    --primary-alpha: rgba(255, 122, 0, 0.1);
    
    --secondary-color: #2c3e50;
    --secondary-dark: #1a252f;
    --secondary-light: #34495e;
    
    --success-color: #10b981;
    --success-light: rgba(16, 185, 129, 0.1);
    --warning-color: #f59e0b;
    --warning-light: rgba(245, 158, 11, 0.1);
    --error-color: #ef4444;
    --error-light: rgba(239, 68, 68, 0.1);
    --info-color: #3b82f6;
    --info-light: rgba(59, 130, 246, 0.1);
    
    /* Enhanced Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
    --font-family-secondary: 'Poppins', sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;
    
    /* Enhanced Spacing System */
    --spacing-px: 1px;
    --spacing-0: 0;
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;
    --spacing-32: 8rem;
    
    /* Enhanced Shadows */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* Enhanced Border Radius */
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Enhanced Transitions */
    --transition-none: none;
    --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-default: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* Enhanced Z-Index Scale */
    --z-0: 0;
    --z-10: 10;
    --z-20: 20;
    --z-30: 30;
    --z-40: 40;
    --z-50: 50;
    --z-auto: auto;
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    
    /* Enhanced Breakpoints */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
    
    /* Enhanced Container Sizes */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* Enhanced Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: var(--line-height-normal);
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
}

body {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    color: var(--text-color);
    background-color: var(--background-color);
    line-height: var(--line-height-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-secondary);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--text-color);
    margin-bottom: var(--spacing-4);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-4);
    line-height: var(--line-height-relaxed);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-default);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* Enhanced Lists */
ul, ol {
    margin-bottom: var(--spacing-4);
    padding-left: var(--spacing-6);
}

li {
    margin-bottom: var(--spacing-1);
}

/* Enhanced Code */
code, pre {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
}

code {
    background-color: var(--background-color);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    color: var(--error-color);
}

pre {
    background-color: var(--secondary-color);
    color: var(--white);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    overflow-x: auto;
    margin-bottom: var(--spacing-4);
}

/* Enhanced Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Enhanced Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-4);
}

th, td {
    padding: var(--spacing-3);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: var(--font-weight-semibold);
    background-color: var(--background-color);
}

/* Enhanced Forms */
input, textarea, select, button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

input, textarea, select {
    width: 100%;
    padding: var(--spacing-3);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background-color: var(--white);
    transition: var(--transition-default);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-alpha);
}

input:invalid, textarea:invalid, select:invalid {
    border-color: var(--error-color);
}

button {
    cursor: pointer;
    border: none;
    background: none;
    padding: 0;
    font: inherit;
}

/* Enhanced Utility Classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
    .container { max-width: var(--container-sm); }
}

@media (min-width: 768px) {
    .container { max-width: var(--container-md); }
}

@media (min-width: 1024px) {
    .container { max-width: var(--container-lg); }
}

@media (min-width: 1280px) {
    .container { max-width: var(--container-xl); }
}

@media (min-width: 1536px) {
    .container { max-width: var(--container-2xl); }
}

/* Enhanced Grid System */
.grid {
    display: grid;
    gap: var(--spacing-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-full { grid-column: 1 / -1; }

/* Enhanced Flexbox Utilities */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* Enhanced Spacing Utilities */
.gap-0 { gap: var(--spacing-0); }
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* Enhanced Text Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-muted { color: var(--text-light); }

/* Enhanced Background Utilities */
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-white { background-color: var(--white); }
.bg-gray { background-color: var(--background-color); }

/* Enhanced Border Utilities */
.border { border: 1px solid var(--border-color); }
.border-2 { border: 2px solid var(--border-color); }
.border-primary { border-color: var(--primary-color); }
.border-success { border-color: var(--success-color); }
.border-error { border-color: var(--error-color); }

.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Enhanced Shadow Utilities */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Enhanced Animation Utilities */
.transition { transition: var(--transition-default); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }
.transition-bounce { transition: var(--transition-bounce); }

.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.hover\:-translate-y-2:hover { transform: translateY(-0.5rem); }

/* Enhanced Responsive Utilities */
@media (min-width: 640px) {
    .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .sm\:flex-row { flex-direction: row; }
    .sm\:text-lg { font-size: var(--font-size-lg); }
}

@media (min-width: 768px) {
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .md\:text-xl { font-size: var(--font-size-xl); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .lg\:text-2xl { font-size: var(--font-size-2xl); }
}

/* Enhanced Accessibility */
@media (prefers-reduced-motion: reduce) {
    .transition,
    .transition-fast,
    .transition-slow,
    .transition-bounce {
        transition: none;
    }
    
    .hover\:scale-105:hover,
    .hover\:scale-110:hover,
    .hover\:-translate-y-1:hover,
    .hover\:-translate-y-2:hover {
        transform: none;
    }
}

@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-light: #333;
    }
    
    .border,
    .border-2 {
        border-color: #000;
    }
}

/* Enhanced Print Styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    a[href]:after {
        content: " (" attr(href) ")";
    }
    
    abbr[title]:after {
        content: " (" attr(title) ")";
    }
    
    .no-print {
        display: none !important;
    }
}
