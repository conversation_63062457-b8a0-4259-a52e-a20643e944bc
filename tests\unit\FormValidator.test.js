import { FormValidator } from '../../assets/scripts/components';

describe('FormValidator', () => {
    describe('validateEmail', () => {
        it('should validate correct email formats', () => {
            expect(FormValidator.validateEmail('<EMAIL>')).toBe(true);
            expect(FormValidator.validateEmail('<EMAIL>')).toBe(true);
            expect(FormValidator.validateEmail('invalid-email')).toBe(false);
            expect(FormValidator.validateEmail('')).toBe(false);
        });
    });

    describe('validatePhone', () => {
        it('should validate correct phone formats', () => {
            expect(FormValidator.validatePhone('1234567890')).toBe(true);
            expect(FormValidator.validatePhone('123456789')).toBe(false);
            expect(FormValidator.validatePhone('12345678901')).toBe(false);
            expect(FormValidator.validatePhone('')).toBe(false);
        });
    });

    describe('validateRequiredFields', () => {
        it('should validate required form fields', () => {
            document.body.innerHTML = `
                <form id="testForm">
                    <input type="text" required value="test" />
                    <input type="email" required value="<EMAIL>" />
                    <input type="text" value="" />
                </form>
            `;

            const form = document.getElementById('testForm');
            expect(FormValidator.validateRequiredFields(form)).toBe(true);
        });
    });
});