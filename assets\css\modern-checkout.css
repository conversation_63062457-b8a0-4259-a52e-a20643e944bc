/* Modern Checkout Page Styles */

/* CSS Custom Properties for Checkout */
:root {
    --checkout-border-radius: 12px;
    --checkout-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --checkout-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --checkout-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --checkout-animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --input-focus-color: rgba(255, 122, 0, 0.1);
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Checkout Page Layout */
.checkout-page {
    background: linear-gradient(135deg, var(--background-color) 0%, #f1f5f9 100%);
    min-height: 100vh;
}

.main-content {
    padding: calc(var(--spacing-xl) + 65px) 0 var(--spacing-xl);
    min-height: calc(100vh - 200px);
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.page-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* Progress Indicator */
.checkout-progress {
    margin: var(--spacing-xl) 0;
    display: flex;
    justify-content: center;
}

.progress-steps {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    z-index: 2;
}

.progress-step::after {
    content: '';
    position: absolute;
    top: 25px;
    left: 50px;
    width: 60px;
    height: 2px;
    background: var(--border-color);
    z-index: 1;
}

.progress-step:last-child::after {
    display: none;
}

.progress-step.completed::after,
.progress-step.active::after {
    background: var(--primary-color);
}

.step-indicator {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 1.2rem;
    transition: var(--checkout-transition);
    position: relative;
    z-index: 2;
}

.progress-step.completed .step-indicator {
    background: #10b981;
    color: var(--white);
}

.progress-step.active .step-indicator {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(255, 122, 0, 0.2);
}

.step-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-light);
    text-align: center;
}

.progress-step.completed .step-label {
    color: #10b981;
    font-weight: 600;
}

.progress-step.active .step-label {
    color: var(--primary-color);
    font-weight: 600;
}

/* Checkout Layout */
.checkout-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-xl);
    align-items: start;
}

/* Checkout Form Section */
.checkout-form-section {
    background: var(--white);
    border-radius: var(--checkout-border-radius);
    box-shadow: var(--checkout-shadow);
    padding: var(--spacing-xl);
    transition: var(--checkout-transition);
}

.checkout-form-section:hover {
    box-shadow: var(--checkout-shadow-hover);
}

/* Form Sections */
.form-section {
    margin-bottom: var(--spacing-xl);
    border: none;
    padding: 0;
}

.form-section:last-of-type {
    margin-bottom: 0;
}

.section-heading {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--background-color);
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.input-group.full-width {
    grid-column: 1 / -1;
}

/* Input Groups */
.input-group {
    margin-bottom: var(--spacing-md);
}

.input-label {
    display: block;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: var(--spacing-sm);
    color: var(--text-light);
    z-index: 2;
    pointer-events: none;
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: calc(var(--spacing-md) + 24px);
    border: 2px solid var(--border-color);
    border-radius: var(--checkout-border-radius);
    font-size: 0.95rem;
    transition: var(--checkout-transition);
    background: var(--white);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--input-focus-color);
}

.form-input:invalid:not(:focus):not(:placeholder-shown) {
    border-color: #ef4444;
}

.form-input:valid:not(:focus):not(:placeholder-shown) {
    border-color: #10b981;
}

/* Select Styling */
select.form-input {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Textarea Styling */
textarea.form-input {
    resize: vertical;
    min-height: 80px;
    padding-top: var(--spacing-sm);
    line-height: 1.5;
}

/* Input Help Text */
.input-help {
    display: block;
    font-size: 0.8rem;
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

/* Error Messages */
.error-message {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: var(--spacing-xs);
    display: none;
    font-weight: 500;
}

.error-message.show {
    display: block;
}

.input-group.error .form-input {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Option Cards (Radio/Checkbox Groups) */
.delivery-options,
.payment-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.option-card {
    position: relative;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--checkout-border-radius);
    cursor: pointer;
    transition: var(--checkout-transition);
    background: var(--white);
    display: block;
}

.option-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--checkout-shadow);
    transform: translateY(-2px);
}

.option-card input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.option-card input[type="radio"]:checked + .option-content {
    color: var(--primary-color);
}

.option-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    text-align: center;
    position: relative;
}

.option-content i {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-xs);
}

.option-title {
    font-weight: 600;
    font-size: 1rem;
}

.option-subtitle {
    font-size: 0.85rem;
    color: var(--text-light);
}

.option-price {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Checkbox Groups */
.checkbox-group {
    margin-bottom: var(--spacing-md);
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--checkout-transition);
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    color: var(--white);
    font-size: 0.8rem;
    font-weight: bold;
}

.checkbox-label input[type="checkbox"]:focus + .checkbox-custom {
    box-shadow: 0 0 0 3px var(--input-focus-color);
}

.checkbox-text {
    font-size: 0.9rem;
    color: var(--text-color);
}

.checkbox-text a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.checkbox-text a:hover {
    text-decoration: underline;
}

/* Delivery Address Section */
.delivery-address-section {
    transition: var(--checkout-transition);
}

.delivery-address-section.hidden {
    opacity: 0.5;
    pointer-events: none;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--checkout-border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--checkout-transition);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--checkout-shadow);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    font-weight: 600;
    flex: 1;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Submit Button Loading State */
.submit-btn .btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    visibility: hidden;
    transition: var(--checkout-transition);
}

.submit-btn.loading .btn-text {
    opacity: 0;
    visibility: hidden;
}

.submit-btn.loading .btn-loader {
    opacity: 1;
    visibility: visible;
}

/* Order Summary Section */
.order-summary-section {
    position: sticky;
    top: calc(65px + var(--spacing-lg));
}

.summary-card {
    background: var(--white);
    border-radius: var(--checkout-border-radius);
    box-shadow: var(--checkout-shadow);
    padding: var(--spacing-xl);
    transition: var(--checkout-transition);
}

.summary-card:hover {
    box-shadow: var(--checkout-shadow-hover);
}

.summary-heading {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--background-color);
}

/* Order Items */
.order-items {
    margin-bottom: var(--spacing-lg);
}

.items-header {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-light);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-sm);
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.order-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    font-size: 0.9rem;
    align-items: center;
}

.item-name {
    font-weight: 500;
    color: var(--text-color);
}

.item-quantity {
    color: var(--text-light);
    text-align: center;
}

.item-price {
    font-weight: 600;
    color: var(--primary-color);
    text-align: right;
}

/* Order Totals */
.order-totals {
    margin-bottom: var(--spacing-lg);
}

.total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 0.95rem;
}

.total-line.final-total {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    padding-top: var(--spacing-sm);
    border-top: 2px solid var(--border-color);
    margin-top: var(--spacing-md);
}

.total-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-md) 0;
}

.delivery-fee-line {
    transition: var(--checkout-transition);
}

.delivery-fee-line.hidden {
    opacity: 0.5;
}

/* Security Badges */
.security-badges {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--checkout-border-radius);
}

.security-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

.security-badge i {
    color: #10b981;
}

/* Delivery Estimate */
.delivery-estimate {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    color: var(--text-color);
    padding: var(--spacing-sm);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border-radius: var(--checkout-border-radius);
    text-align: center;
}

.delivery-time {
    font-weight: 600;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: calc(65px + var(--spacing-md));
    right: var(--spacing-md);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--white);
    border-radius: var(--checkout-border-radius);
    box-shadow: var(--checkout-shadow-hover);
    padding: var(--spacing-md);
    max-width: 300px;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    animation: slideInRight 0.3s var(--checkout-animation-bounce);
}

.toast.success {
    border-left: 4px solid #10b981;
}

.toast.error {
    border-left: 4px solid #ef4444;
}

.toast.warning {
    border-left: 4px solid #f59e0b;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .checkout-layout {
        grid-template-columns: 1fr 350px;
    }
}

@media (max-width: 768px) {
    .checkout-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .order-summary-section {
        position: static;
        order: -1;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .delivery-options,
    .payment-options {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column-reverse;
    }

    .progress-steps {
        gap: var(--spacing-md);
    }

    .progress-step::after {
        width: 40px;
        left: 40px;
    }

    .step-indicator {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .step-label {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: calc(var(--spacing-lg) + 65px) 0 var(--spacing-lg);
    }

    .page-title {
        font-size: 2rem;
    }

    .checkout-form-section,
    .summary-card {
        padding: var(--spacing-lg);
    }

    .progress-steps {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .progress-step::after {
        top: 40px;
        left: 50%;
        transform: translateX(-50%);
        width: 2px;
        height: 20px;
    }

    .option-card {
        padding: var(--spacing-sm);
    }

    .items-header,
    .order-item {
        grid-template-columns: 1fr auto;
        gap: var(--spacing-xs);
    }

    .item-quantity {
        display: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .form-input,
    .option-card {
        border-width: 3px;
    }

    .btn {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    .checkout-page {
        background: white;
    }

    .header,
    .form-actions,
    .toast-container {
        display: none;
    }

    .checkout-layout {
        grid-template-columns: 1fr;
    }

    .summary-card {
        box-shadow: none;
        border: 1px solid #000;
    }
}
