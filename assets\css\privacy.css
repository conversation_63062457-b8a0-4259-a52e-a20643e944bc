/* Privacy Page Styles */
.privacy-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: var(--spacing-xxl) 0;
    color: var(--white);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.privacy-hero-title {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.privacy-hero-text {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.privacy-content {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
}

.privacy-navigation {
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-md));
    height: fit-content;
}

.privacy-nav ul {
    list-style: none;
    padding: 0;
}

.privacy-nav-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-color);
    text-decoration: none;
    border-left: 3px solid transparent;
    transition: all var(--transition);
}

.privacy-nav-link:hover,
.privacy-nav-link.active {
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    background: var(--background-light);
}

.privacy-section {
    margin-bottom: var(--spacing-xl);
}

.privacy-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition), box-shadow var(--transition);
}

.privacy-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.privacy-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.privacy-list {
    list-style: none;
    padding: 0;
    margin: var(--spacing-md) 0;
}

.privacy-list li {
    padding: var(--spacing-xs) 0;
    padding-left: var(--spacing-md);
    position: relative;
}

.privacy-list li::before {
    content: "•";
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

@media (max-width: 768px) {
    .privacy-content {
        grid-template-columns: 1fr;
    }

    .privacy-navigation {
        position: relative;
        top: 0;
        margin-bottom: var(--spacing-lg);
    }

    .privacy-nav ul {
        display: flex;
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }

    .privacy-nav-link {
        white-space: nowrap;
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .privacy-nav-link:hover,
    .privacy-nav-link.active {
        border-left-color: transparent;
        border-bottom-color: var(--primary-color);
    }
}
