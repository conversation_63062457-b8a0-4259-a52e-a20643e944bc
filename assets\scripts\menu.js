// --- <PERSON><PERSON> (`menu.html`) specific functionality
function initMenuPage() {
    const categoryNav = document.querySelector('.category-nav');
    const categoryLinks = document.querySelectorAll('.category-link');
    const menuSections = document.querySelectorAll('.menu-section');
    const menuCategories = document.querySelectorAll('.menu-category');

    // Function to update active link
    function updateActiveLink(sectionId) {
        categoryLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${sectionId}`) {
                link.classList.add('active');
                // Also activate parent if it's a sub-category
                const parentLi = link.closest('.category-item');
                 if(parentLi){
                  const parentLink = parentLi.querySelector(':scope > .category-link') //direct child
                    if(parentLink) {
                        parentLink.classList.add('active');
                    }
                 }
            }
        });
    }


     // Intersection Observer for scroll detection - observe both sections and categories
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting ) {
               // && entry.intersectionRatio >= 0.5 - removed this to make the snapping more responsive
                // console.log("Intersecting:", entry.target.id, entry.intersectionRatio); // Debugging
                updateActiveLink(entry.target.id);
            }
        });
    }, {
        threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0], //observe multiple thresholds
        rootMargin: '-50px 0px -40% 0px'  // Adjusted for better responsiveness.
    });


    // Observe all menu sections *and* categories
    menuSections.forEach(section => {
        observer.observe(section);
    });
    menuCategories.forEach(category => {
        observer.observe(category);
    });


    // Handle click events on category links
    categoryNav.addEventListener('click', (event) => {
        const link = event.target.closest('.category-link');
        if (!link) return;

        event.preventDefault();

        // Get the target section id
        const targetId = link.getAttribute('href').substring(1);
        const targetSection = document.getElementById(targetId);

        if (targetSection) {
            // Update active state
            updateActiveLink(targetId);

            // Scroll to section
            targetSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });

      // Set initial active state based on current scroll position
    function setInitialActiveSection() {
      const sections = Array.from(menuSections);
      const categories = Array.from(menuCategories);
      const allElements = sections.concat(categories);

      for (const element of allElements) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
              updateActiveLink(element.id);
              break; // Stop after finding the first visible section/category.
          }
        }
    }

    // Set initial active state on page load
    setInitialActiveSection();
}