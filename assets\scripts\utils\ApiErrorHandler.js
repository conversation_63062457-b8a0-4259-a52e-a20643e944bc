export class ApiError extends Error {
    constructor(message, code, status) {
        super(message);
        this.name = 'ApiError';
        this.code = code;
        this.status = status;
    }
}

export class ApiErrorHandler {
    static async handleResponse(response) {
        if (!response.ok) {
            const error = await response.json().catch(() => ({}));
            
            throw new ApiError(
                error.message || 'An unexpected error occurred',
                error.code || 'UNKNOWN_ERROR',
                response.status
            );
        }
        return response.json();
    }

    static getErrorMessage(error) {
        if (error instanceof ApiError) {
            switch (error.status) {
                case 400:
                    return 'Invalid request. Please check your input.';
                case 401:
                    return 'Please log in to continue.';
                case 403:
                    return 'You do not have permission to perform this action.';
                case 404:
                    return 'The requested resource was not found.';
                case 429:
                    return 'Too many requests. Please try again later.';
                case 500:
                    return 'Server error. Please try again later.';
                default:
                    return error.message;
            }
        }

        if (error.name === 'AbortError') {
            return 'Request timed out. Please try again.';
        }

        if (!navigator.onLine) {
            return 'No internet connection. Please check your network.';
        }

        return 'An unexpected error occurred. Please try again.';
    }

    static handleError(error, context = '') {
        // Log error for debugging
        console.error(`API Error [${context}]:`, error);

        // Log to error tracking service if available
        ErrorBoundary.logError(error, { context: `api_${context}` });

        // Return user-friendly message
        return this.getErrorMessage(error);
    }
}