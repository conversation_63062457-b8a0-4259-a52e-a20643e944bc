<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Cart Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .test-pass {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-fail {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .test-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .cart-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Simple Cart Test</h1>
        <p>This test helps verify cart functionality and provides debugging information.</p>
        
        <div>
            <button onclick="clearStorage()">Clear Cart Storage</button>
            <button onclick="addTestItems()">Add Test Items</button>
            <button onclick="checkCartStorage()">Check Cart Storage</button>
            <button onclick="openCartPage()">Open Cart Page</button>
        </div>
        
        <div id="test-results"></div>
        
        <div class="cart-info">
            <h3>Current Cart Status:</h3>
            <div id="cart-status">Click "Check Cart Storage" to see current cart data</div>
        </div>
        
        <div class="cart-info">
            <h3>Instructions for Manual Testing:</h3>
            <ol>
                <li>Click "Clear Cart Storage" to start fresh</li>
                <li>Click "Add Test Items" to populate the cart</li>
                <li>Click "Open Cart Page" to test the actual cart page</li>
                <li>Verify that:
                    <ul>
                        <li>The page loads completely (no blank/black screen)</li>
                        <li>Header is visible</li>
                        <li>Cart content area is visible</li>
                        <li>Either cart items or "empty cart" message is shown</li>
                        <li>Order summary section is visible</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script>
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearStorage() {
            localStorage.removeItem('cartItems');
            logResult('Cart storage cleared', 'info');
            checkCartStorage();
        }

        function addTestItems() {
            const testItems = [
                {
                    id: 'test-pizza-1',
                    title: 'Margherita Pizza',
                    price: '₦2500.00',
                    quantity: 2,
                    image: 'assets/images/placeholder.svg'
                },
                {
                    id: 'test-burger-1',
                    title: 'Classic Burger',
                    price: '₦1800.00',
                    quantity: 1,
                    image: 'assets/images/placeholder.svg'
                },
                {
                    id: 'test-pasta-1',
                    title: 'Spaghetti Carbonara',
                    price: '₦2200.00',
                    quantity: 1,
                    image: 'assets/images/placeholder.svg'
                }
            ];
            
            localStorage.setItem('cartItems', JSON.stringify(testItems));
            logResult(`Added ${testItems.length} test items to cart`, 'pass');
            checkCartStorage();
        }

        function checkCartStorage() {
            const cartData = localStorage.getItem('cartItems');
            const statusDiv = document.getElementById('cart-status');
            
            if (cartData) {
                try {
                    const items = JSON.parse(cartData);
                    const totalItems = items.reduce((sum, item) => sum + (item.quantity || 1), 0);
                    const totalValue = items.reduce((sum, item) => {
                        const price = parseFloat(item.price.replace(/[^\d.-]/g, ''));
                        return sum + (price * (item.quantity || 1));
                    }, 0);
                    
                    statusDiv.innerHTML = `
                        <strong>Cart contains ${items.length} unique items (${totalItems} total items)</strong><br>
                        <strong>Total value: ₦${totalValue.toFixed(2)}</strong><br><br>
                        <strong>Items:</strong><br>
                        ${items.map(item => `• ${item.title} - ${item.price} x ${item.quantity}`).join('<br>')}
                    `;
                    
                    logResult(`Cart contains ${items.length} items with total value ₦${totalValue.toFixed(2)}`, 'pass');
                } catch (error) {
                    statusDiv.innerHTML = `<span style="color: red;">Error parsing cart data: ${error.message}</span>`;
                    logResult(`Error parsing cart data: ${error.message}`, 'fail');
                }
            } else {
                statusDiv.innerHTML = '<em>Cart is empty</em>';
                logResult('Cart is empty', 'info');
            }
        }

        function openCartPage() {
            logResult('Opening cart page...', 'info');
            window.open('cart.html', '_blank');
        }

        // Auto-check cart status when page loads
        window.addEventListener('load', () => {
            checkCartStorage();
            logResult('Cart test page loaded', 'info');
        });
    </script>
</body>
</html>
