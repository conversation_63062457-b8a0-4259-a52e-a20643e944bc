# Magic Menu Website

A modern, responsive restaurant website with online ordering capabilities.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build
```

## 📚 Documentation

- [Developer Guide](docs/developer-guide.md)
- [API Documentation](docs/api.md)
- [Component Library](docs/components.md)
- [Testing Guide](docs/testing.md)
- [Deployment Guide](docs/deployment.md)

## 🛠 Tech Stack

- Vanilla JavaScript (ES6+)
- CSS3 with SCSS
- Jest for testing
- Cypress for E2E testing

## 🔑 Key Features

- Responsive design
- Interactive menu with filtering
- Real-time cart management
- Smooth animations
- Performance optimized
- Accessibility compliant

## 📦 Project Structure

```
magic-menu/
├── assets/
│   ├── scripts/
│   ├── styles/
│   └── images/
├── docs/
├── tests/
└── public/
```

## 🤝 Contributing

See our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

MIT License - see [LICENSE](LICENSE) for details