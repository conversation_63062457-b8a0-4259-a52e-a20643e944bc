import Analytics from './services/Analytics.js';
import { config } from './config/env.js';

// 1. Basic event tracking with consent check
function trackEvent(eventName, data) {
    if (!Analytics.consent) return;
    
    Analytics.track(eventName, data);
}

// 2. Page view tracking
function trackPageView() {
    if (!Analytics.consent) return;

    Analytics.trackPageView(document.title);
}

// 3. E-commerce tracking examples
const ecommerceTracking = {
    // Track product view
    viewProduct(product) {
        if (!Analytics.consent) return;

        Analytics.trackEcommerce('view_item', {
            id: product.id,
            name: product.name,
            price: product.price,
            category: product.category
        });
    },

    // Track add to cart
    addToCart(item) {
        if (!Analytics.consent) return;

        Analytics.trackEcommerce('add_to_cart', {
            id: item.id,
            name: item.name,
            price: item.price,
            quantity: item.quantity || 1,
            category: item.category
        });
    },

    // Track checkout start
    beginCheckout(cart) {
        if (!Analytics.consent) return;

        Analytics.trackEcommerce('begin_checkout', {
            items: cart.items,
            value: cart.total,
            shipping: cart.shipping,
            tax: cart.tax
        });
    },

    // Track purchase completion
    completePurchase(order) {
        if (!Analytics.consent) return;

        Analytics.trackEcommerce('purchase', {
            transaction_id: order.id,
            value: order.total,
            tax: order.tax,
            shipping: order.shipping,
            items: order.items
        });
    }
};

// 4. Debug logging helper
function debugLog(eventName, data) {
    if (config.IS_DEVELOPMENT) {
        console.debug('Analytics Event:', eventName, data);
    }
}

// Usage examples:
document.addEventListener('DOMContentLoaded', () => {
    // Track page view on load
    trackPageView();

    // Track product views
    document.querySelectorAll('.product-card').forEach(card => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const product = {
                            id: card.dataset.productId,
                            name: card.dataset.name,
                            price: parseFloat(card.dataset.price),
                            category: card.dataset.category
                        };
                        ecommerceTracking.viewProduct(product);
                    }
                });
            },
            { threshold: 0.5 }
        );
        observer.observe(card);
    });

    // Track add to cart events
    document.addEventListener('cartUpdated', (e) => {
        if (e.detail?.action === 'add') {
            ecommerceTracking.addToCart(e.detail.item);
        }
    });

    // Track custom events
    document.addEventListener('userAction', (e) => {
        trackEvent('user_action', {
            action: e.detail.action,
            timestamp: new Date().toISOString()
        });
    });
});