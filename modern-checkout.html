<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magic Menu - Secure Checkout</title>
    
    <!-- Critical CSS -->
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/modern-checkout.css">
    
    <!-- External Dependencies -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">
    
    <!-- Favicons -->
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="manifest" href="assets/images/site.webmanifest">
    <meta name="theme-color" content="#ff7a00">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Complete your order securely at Magic Menu. Fast checkout with multiple payment options and secure SSL encryption.">
    <meta name="keywords" content="secure checkout, food delivery payment, Magic Menu order, online payment">
    <meta name="author" content="Magic Menu">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Secure Checkout - Magic Menu">
    <meta property="og:description" content="Complete your food order with our secure checkout process">
    <meta property="og:image" content="assets/images/og-checkout.jpg">
    <meta property="og:url" content="https://magicmenu.ng/checkout">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Secure Checkout - Magic Menu">
    <meta name="twitter:description" content="Complete your food order with our secure checkout process">
    <meta name="twitter:image" content="assets/images/twitter-checkout.jpg">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Checkout - Magic Menu",
        "description": "Secure checkout process for food orders",
        "url": "https://magicmenu.ng/checkout",
        "isPartOf": {
            "@type": "WebSite",
            "name": "Magic Menu",
            "url": "https://magicmenu.ng"
        }
    }
    </script>
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="assets/css/styles.css" as="style">
    <link rel="preload" href="assets/css/modern-checkout.css" as="style">
    <link rel="preload" href="assets/scripts/modern-checkout.js" as="script">
</head>
<body class="checkout-page">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header -->
    <header class="header" role="banner">
        <div class="container header-content">
            <a href="index.html" class="logo" aria-label="Magic Menu - Go to homepage">
                <img src="assets/images/logo.png" alt="Magic Menu Logo" width="40" height="40">
                <span class="logo-text">Magic Menu</span>
            </a>
            
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="menu.html" class="nav-link">Menu</a></li>
                    <li><a href="modern-cart.html" class="nav-link">Order</a></li>
                    <li><a href="account.html" class="nav-link">Account</a></li>
                    <li><a href="contact.html" class="nav-link">Contact</a></li>
                </ul>
            </nav>
            
            <div class="cart-icon-wrapper">
                <button class="cart-icon" aria-label="View cart" aria-describedby="cart-count">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span id="cart-count" class="cart-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Page Header -->
            <header class="page-header">
                <h1 class="page-title">Secure Checkout</h1>
                <p class="page-subtitle">Complete your order with confidence</p>
            </header>

            <!-- Progress Indicator -->
            <nav class="checkout-progress" role="navigation" aria-label="Checkout progress">
                <ol class="progress-steps">
                    <li class="progress-step completed">
                        <div class="step-indicator">
                            <i class="fas fa-check" aria-hidden="true"></i>
                        </div>
                        <span class="step-label">Review Order</span>
                    </li>
                    <li class="progress-step active" aria-current="step">
                        <div class="step-indicator">
                            <i class="fas fa-credit-card" aria-hidden="true"></i>
                        </div>
                        <span class="step-label">Payment</span>
                    </li>
                    <li class="progress-step">
                        <div class="step-indicator">
                            <i class="fas fa-check-circle" aria-hidden="true"></i>
                        </div>
                        <span class="step-label">Confirmation</span>
                    </li>
                </ol>
            </nav>

            <!-- Checkout Layout -->
            <div class="checkout-layout">
                <!-- Checkout Form Section -->
                <section class="checkout-form-section" aria-labelledby="checkout-form-heading">
                    <form id="checkout-form" class="checkout-form" novalidate>
                        <!-- Delivery Method -->
                        <fieldset class="form-section">
                            <legend class="section-heading">
                                <i class="fas fa-truck" aria-hidden="true"></i>
                                Delivery Method
                            </legend>
                            
                            <div class="delivery-options" role="radiogroup" aria-labelledby="delivery-method-legend">
                                <label class="option-card">
                                    <input type="radio" name="deliveryMethod" value="delivery" checked required>
                                    <div class="option-content">
                                        <i class="fas fa-truck-fast" aria-hidden="true"></i>
                                        <span class="option-title">Delivery</span>
                                        <span class="option-subtitle">30-45 min</span>
                                        <span class="option-price">₦500.00</span>
                                    </div>
                                </label>
                                
                                <label class="option-card">
                                    <input type="radio" name="deliveryMethod" value="pickup" required>
                                    <div class="option-content">
                                        <i class="fas fa-store" aria-hidden="true"></i>
                                        <span class="option-title">Pickup</span>
                                        <span class="option-subtitle">15-20 min</span>
                                        <span class="option-price">Free</span>
                                    </div>
                                </label>
                            </div>
                        </fieldset>

                        <!-- Customer Information -->
                        <fieldset class="form-section">
                            <legend class="section-heading">
                                <i class="fas fa-user" aria-hidden="true"></i>
                                Customer Information
                            </legend>
                            
                            <div class="form-grid">
                                <div class="input-group">
                                    <label for="customer-name" class="input-label">Full Name *</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-user input-icon" aria-hidden="true"></i>
                                        <input type="text" 
                                               id="customer-name" 
                                               name="customerName" 
                                               class="form-input" 
                                               required 
                                               autocomplete="name"
                                               aria-describedby="customer-name-error">
                                    </div>
                                    <div id="customer-name-error" class="error-message" role="alert"></div>
                                </div>
                                
                                <div class="input-group">
                                    <label for="customer-email" class="input-label">Email Address *</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-envelope input-icon" aria-hidden="true"></i>
                                        <input type="email" 
                                               id="customer-email" 
                                               name="customerEmail" 
                                               class="form-input" 
                                               required 
                                               autocomplete="email"
                                               aria-describedby="customer-email-error">
                                    </div>
                                    <div id="customer-email-error" class="error-message" role="alert"></div>
                                </div>
                                
                                <div class="input-group">
                                    <label for="customer-phone" class="input-label">Phone Number *</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-phone input-icon" aria-hidden="true"></i>
                                        <input type="tel" 
                                               id="customer-phone" 
                                               name="customerPhone" 
                                               class="form-input" 
                                               required 
                                               autocomplete="tel"
                                               placeholder="+234 XXX XXX XXXX"
                                               aria-describedby="customer-phone-error">
                                    </div>
                                    <div id="customer-phone-error" class="error-message" role="alert"></div>
                                </div>
                            </div>
                        </fieldset>

                        <!-- Delivery Address (shown only for delivery) -->
                        <fieldset class="form-section delivery-address-section">
                            <legend class="section-heading">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                Delivery Address
                            </legend>
                            
                            <div class="form-grid">
                                <div class="input-group full-width">
                                    <label for="delivery-address" class="input-label">Street Address *</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-home input-icon" aria-hidden="true"></i>
                                        <input type="text" 
                                               id="delivery-address" 
                                               name="deliveryAddress" 
                                               class="form-input" 
                                               autocomplete="street-address"
                                               aria-describedby="delivery-address-error">
                                    </div>
                                    <div id="delivery-address-error" class="error-message" role="alert"></div>
                                </div>
                                
                                <div class="input-group">
                                    <label for="delivery-city" class="input-label">City *</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-city input-icon" aria-hidden="true"></i>
                                        <input type="text" 
                                               id="delivery-city" 
                                               name="deliveryCity" 
                                               class="form-input" 
                                               autocomplete="address-level2"
                                               aria-describedby="delivery-city-error">
                                    </div>
                                    <div id="delivery-city-error" class="error-message" role="alert"></div>
                                </div>
                                
                                <div class="input-group">
                                    <label for="delivery-state" class="input-label">State *</label>
                                    <div class="input-wrapper">
                                        <i class="fas fa-map input-icon" aria-hidden="true"></i>
                                        <select id="delivery-state" 
                                                name="deliveryState" 
                                                class="form-input" 
                                                autocomplete="address-level1"
                                                aria-describedby="delivery-state-error">
                                            <option value="">Select State</option>
                                            <option value="lagos">Lagos</option>
                                            <option value="abuja">Abuja</option>
                                            <option value="kano">Kano</option>
                                            <option value="rivers">Rivers</option>
                                            <option value="oyo">Oyo</option>
                                        </select>
                                    </div>
                                    <div id="delivery-state-error" class="error-message" role="alert"></div>
                                </div>
                            </div>
                            
                            <div class="input-group">
                                <label for="delivery-notes" class="input-label">Delivery Instructions (Optional)</label>
                                <div class="input-wrapper">
                                    <i class="fas fa-sticky-note input-icon" aria-hidden="true"></i>
                                    <textarea id="delivery-notes" 
                                              name="deliveryNotes" 
                                              class="form-input" 
                                              rows="3" 
                                              placeholder="Any special instructions for delivery..."
                                              maxlength="500"></textarea>
                                </div>
                                <small class="input-help">Maximum 500 characters</small>
                            </div>
                        </fieldset>

                        <!-- Payment Method -->
                        <fieldset class="form-section">
                            <legend class="section-heading">
                                <i class="fas fa-credit-card" aria-hidden="true"></i>
                                Payment Method
                            </legend>
                            
                            <div class="payment-options" role="radiogroup" aria-labelledby="payment-method-legend">
                                <label class="option-card">
                                    <input type="radio" name="paymentMethod" value="card" checked required>
                                    <div class="option-content">
                                        <i class="fas fa-credit-card" aria-hidden="true"></i>
                                        <span class="option-title">Credit/Debit Card</span>
                                        <span class="option-subtitle">Visa, Mastercard, Verve</span>
                                    </div>
                                </label>
                                
                                <label class="option-card">
                                    <input type="radio" name="paymentMethod" value="transfer" required>
                                    <div class="option-content">
                                        <i class="fas fa-university" aria-hidden="true"></i>
                                        <span class="option-title">Bank Transfer</span>
                                        <span class="option-subtitle">Direct bank transfer</span>
                                    </div>
                                </label>
                                
                                <label class="option-card">
                                    <input type="radio" name="paymentMethod" value="cash" required>
                                    <div class="option-content">
                                        <i class="fas fa-money-bill-wave" aria-hidden="true"></i>
                                        <span class="option-title">Cash on Delivery</span>
                                        <span class="option-subtitle">Pay when you receive</span>
                                    </div>
                                </label>
                            </div>
                        </fieldset>

                        <!-- Order Notes -->
                        <fieldset class="form-section">
                            <legend class="section-heading">
                                <i class="fas fa-comment" aria-hidden="true"></i>
                                Order Notes (Optional)
                            </legend>
                            
                            <div class="input-group">
                                <label for="order-notes" class="input-label">Special Requests</label>
                                <div class="input-wrapper">
                                    <i class="fas fa-edit input-icon" aria-hidden="true"></i>
                                    <textarea id="order-notes" 
                                              name="orderNotes" 
                                              class="form-input" 
                                              rows="4" 
                                              placeholder="Any special requests for your order..."
                                              maxlength="1000"></textarea>
                                </div>
                                <small class="input-help">Maximum 1000 characters</small>
                            </div>
                        </fieldset>

                        <!-- Terms and Conditions -->
                        <div class="form-section">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="terms-agreement" 
                                           name="termsAgreement" 
                                           required 
                                           aria-describedby="terms-error">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">
                                        I agree to the <a href="terms.html" target="_blank" rel="noopener">Terms and Conditions</a> 
                                        and <a href="privacy.html" target="_blank" rel="noopener">Privacy Policy</a>
                                    </span>
                                </label>
                                <div id="terms-error" class="error-message" role="alert"></div>
                            </div>
                            
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="marketing-consent" 
                                           name="marketingConsent">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">
                                        I would like to receive promotional emails and updates about new menu items
                                    </span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-actions">
                            <button type="submit" class="submit-btn btn btn-primary btn-large" disabled>
                                <i class="fas fa-lock" aria-hidden="true"></i>
                                <span class="btn-text">Complete Order</span>
                                <span class="btn-loader" aria-hidden="true">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    Processing...
                                </span>
                            </button>
                            
                            <a href="modern-cart.html" class="btn btn-outline">
                                <i class="fas fa-arrow-left" aria-hidden="true"></i>
                                Back to Cart
                            </a>
                        </div>
                    </form>
                </section>

                <!-- Order Summary Section -->
                <aside class="order-summary-section" aria-labelledby="order-summary-heading">
                    <div class="summary-card">
                        <h2 id="order-summary-heading" class="summary-heading">
                            <i class="fas fa-receipt" aria-hidden="true"></i>
                            Order Summary
                        </h2>
                        
                        <!-- Order Items -->
                        <div class="order-items">
                            <div class="items-header">
                                <span>Items</span>
                                <span>Qty</span>
                                <span>Price</span>
                            </div>
                            <div class="items-list">
                                <!-- Items will be dynamically inserted here -->
                            </div>
                        </div>
                        
                        <!-- Order Totals -->
                        <div class="order-totals">
                            <div class="total-line">
                                <span>Subtotal</span>
                                <span class="order-subtotal">₦0.00</span>
                            </div>
                            <div class="total-line">
                                <span>Tax (7.5%)</span>
                                <span class="order-tax">₦0.00</span>
                            </div>
                            <div class="total-line delivery-fee-line">
                                <span>Delivery Fee</span>
                                <span class="order-delivery">₦500.00</span>
                            </div>
                            <div class="total-divider"></div>
                            <div class="total-line final-total">
                                <span>Total</span>
                                <span class="order-total">₦500.00</span>
                            </div>
                        </div>
                        
                        <!-- Security Badges -->
                        <div class="security-badges">
                            <div class="security-badge">
                                <i class="fas fa-shield-alt" aria-hidden="true"></i>
                                <span>SSL Secured</span>
                            </div>
                            <div class="security-badge">
                                <i class="fas fa-lock" aria-hidden="true"></i>
                                <span>256-bit Encryption</span>
                            </div>
                        </div>
                        
                        <!-- Estimated Delivery Time -->
                        <div class="delivery-estimate">
                            <i class="fas fa-clock" aria-hidden="true"></i>
                            <span>Estimated delivery: <strong class="delivery-time">30-45 minutes</strong></span>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="toast-container" aria-live="polite" aria-atomic="true"></div>

    <!-- Scripts -->
    <script type="module" src="assets/scripts/modern-checkout.js"></script>
</body>
</html>
