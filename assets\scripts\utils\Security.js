export class Security {
    static get PASSWORD_MIN_LENGTH() {
        return 12;
    }
    static PASSWORD_PATTERN = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,}$/;

    static sanitizeInput(input) {
        if (typeof input !== 'string') return input;
        return input
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    }

    static validatePassword(password) {
        if (!password || password.length < this.PASSWORD_MIN_LENGTH) {
            return {
                isValid: false,
                message: `Password must be at least ${this.PASSWORD_MIN_LENGTH} characters long`
            };
        }

        if (!this.PASSWORD_PATTERN.test(password)) {
            return {
                isValid: false,
                message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
            };
        }

        return { isValid: true };
    }

    static hashPassword(password) {
        // Use Web Crypto API for client-side hashing
        return crypto.subtle.digest('SHA-256', new TextEncoder().encode(password))
            .then(hash => Array.from(new Uint8Array(hash))
                .map(b => b.toString(16).padStart(2, '0'))
                .join(''));
    }

    static generateNonce() {
        const array = new Uint8Array(16);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }

    static validateAndSanitizeFormData(formData) {
        const sanitized = new FormData();
        for (const [key, value] of formData.entries()) {
            sanitized.append(key, this.sanitizeInput(value));
        }
        return sanitized;
    }
}
