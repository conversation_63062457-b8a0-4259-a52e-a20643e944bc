/* About Page Styles */

.about-hero {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                url('../images/about-hero-bg.jpg') center/cover;
    padding: calc(var(--header-height) + var(--spacing-xl)) 0 var(--spacing-xl);
    color: var(--white);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: var(--spacing-sm);
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Journey Section */
.journey-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-color);
}

.journey-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto var(--spacing-xl);
}

.journey-content h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.timeline {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-lg) 0;
}

.timeline-item {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.timeline .year {
    font-weight: 700;
    color: var(--primary-color);
    min-width: 80px;
}

/* Values Section */
.values-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.section-title {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xl);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.value-card {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.value-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.value-card h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-dark);
}

/* Team Section */
.team-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-color);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.team-member {
    text-align: center;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.team-member img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.team-member h3 {
    margin: var(--spacing-sm) 0;
    color: var(--text-dark);
}

.team-member .role {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.team-member .bio {
    padding: 0 var(--spacing-md) var(--spacing-md);
    color: var(--text-light);
}

/* Awards Section */
.awards-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.awards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.award {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.award i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.award h3 {
    margin-bottom: var(--spacing-xs);
    color: var(--text-dark);
}

.award p {
    color: var(--text-light);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .timeline-item {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .timeline .year {
        min-width: auto;
    }

    .team-member img {
        height: 250px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .value-card,
    .award {
        padding: var(--spacing-md);
    }
}