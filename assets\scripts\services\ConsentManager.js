import Analytics from './Analytics.js';

class ConsentManager {
    constructor() {
        this.cookieBanner = null;
        this.consentStatus = this.getStoredConsent();
    }

    init() {
        if (!this.consentStatus) {
            this.showConsentBanner();
        } else {
            Analytics.setConsent(this.consentStatus === 'granted');
        }
    }

    createConsentBanner() {
        const banner = document.createElement('div');
        banner.className = 'consent-banner';
        banner.innerHTML = `
            <div class="consent-content">
                <p>We use cookies and similar technologies to improve your experience and analyze traffic. 
                   For more information, see our <a href="/privacy">Privacy Policy</a>.</p>
                <div class="consent-buttons">
                    <button class="btn btn-secondary" data-consent="decline">Decline</button>
                    <button class="btn btn-primary" data-consent="accept">Accept</button>
                </div>
            </div>
        `;

        banner.addEventListener('click', (e) => {
            const button = e.target.closest('[data-consent]');
            if (button) {
                this.handleConsent(button.dataset.consent === 'accept');
            }
        });

        return banner;
    }

    showConsentBanner() {
        this.cookieBanner = this.createConsentBanner();
        document.body.appendChild(this.cookieBanner);
    }

    handleConsent(granted) {
        this.consentStatus = granted ? 'granted' : 'denied';
        localStorage.setItem('analytics_consent', this.consentStatus);
        Analytics.setConsent(granted);
        
        if (this.cookieBanner) {
            this.cookieBanner.remove();
            this.cookieBanner = null;
        }
    }

    getStoredConsent() {
        return localStorage.getItem('analytics_consent');
    }
}

export default new ConsentManager();