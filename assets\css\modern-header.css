/* Modern Header Component Styles */

/* Header Container */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed, 1030);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: var(--transition-default, all 0.3s ease);
    height: 65px;
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 65px;
    padding: 0 var(--spacing-4, 1rem);
    max-width: var(--container-xl, 1280px);
    margin: 0 auto;
}

/* Logo */
.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    text-decoration: none;
    color: var(--text-color, #333);
    font-weight: var(--font-weight-bold, 700);
    font-size: var(--font-size-lg, 1.125rem);
    transition: var(--transition-default, all 0.3s ease);
    z-index: var(--z-50, 50);
}

.logo:hover {
    color: var(--primary-color, #ff7a00);
    transform: scale(1.05);
}

.logo img {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg, 0.5rem);
    transition: var(--transition-default, all 0.3s ease);
}

.logo:hover img {
    transform: rotate(5deg);
}

.logo-text {
    font-family: var(--font-family-secondary, 'Poppins', sans-serif);
    background: linear-gradient(135deg, var(--primary-color, #ff7a00), var(--primary-dark, #e66d00));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation */
.main-nav {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-6, 1.5rem);
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-2, 0.5rem) var(--spacing-3, 0.75rem);
    color: var(--text-color, #333);
    text-decoration: none;
    font-weight: var(--font-weight-medium, 500);
    font-size: var(--font-size-sm, 0.875rem);
    border-radius: var(--radius-lg, 0.5rem);
    transition: var(--transition-default, all 0.3s ease);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color, #ff7a00);
    background: rgba(255, 122, 0, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--primary-color, #ff7a00);
    background: rgba(255, 122, 0, 0.1);
    font-weight: var(--font-weight-semibold, 600);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: var(--primary-color, #ff7a00);
    border-radius: 1px;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: var(--z-50, 50);
    transition: var(--transition-default, all 0.3s ease);
}

.mobile-menu-toggle:hover {
    background: rgba(255, 122, 0, 0.1);
    border-radius: var(--radius-lg, 0.5rem);
}

.hamburger-line {
    width: 20px;
    height: 2px;
    background: var(--text-color, #333);
    margin: 2px 0;
    transition: var(--transition-default, all 0.3s ease);
    border-radius: 1px;
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Cart Icon */
.cart-icon-wrapper {
    display: flex;
    align-items: center;
    z-index: var(--z-50, 50);
}

.cart-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: var(--primary-color, #ff7a00);
    color: var(--white, #fff);
    border: none;
    border-radius: var(--radius-full, 50%);
    cursor: pointer;
    transition: var(--transition-default, all 0.3s ease);
    font-size: 1.1rem;
}

.cart-icon:hover {
    background: var(--primary-dark, #e66d00);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--error-color, #ef4444);
    color: var(--white, #fff);
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: var(--font-weight-bold, 700);
    min-width: 20px;
    height: 20px;
    border-radius: var(--radius-full, 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--white, #fff);
    animation: pulse 2s infinite;
}

.cart-count:empty {
    display: none;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color, #ff7a00);
    color: var(--white, #fff);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: var(--z-tooltip, 1070);
    transition: top 0.3s;
    font-size: var(--font-size-sm, 0.875rem);
    font-weight: var(--font-weight-medium, 500);
}

.skip-link:focus {
    top: 6px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
        order: 2;
    }
    
    .main-nav {
        position: fixed;
        top: 65px;
        left: 0;
        right: 0;
        background: var(--white, #fff);
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-default, all 0.3s ease);
        z-index: var(--z-dropdown, 1000);
        box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    }
    
    .main-nav.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-list {
        flex-direction: column;
        padding: var(--spacing-4, 1rem);
        gap: 0;
        width: 100%;
    }
    
    .nav-link {
        width: 100%;
        padding: var(--spacing-4, 1rem);
        border-radius: var(--radius-lg, 0.5rem);
        margin-bottom: var(--spacing-2, 0.5rem);
        font-size: var(--font-size-base, 1rem);
        justify-content: center;
    }
    
    .nav-link.active::after {
        display: none;
    }
    
    .cart-icon-wrapper {
        order: 3;
    }
    
    .logo {
        order: 1;
        flex: 1;
    }
    
    .header-content {
        padding: 0 var(--spacing-3, 0.75rem);
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 var(--spacing-2, 0.5rem);
    }
    
    .logo {
        font-size: var(--font-size-base, 1rem);
    }
    
    .logo img {
        width: 32px;
        height: 32px;
    }
    
    .cart-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .cart-count {
        min-width: 18px;
        height: 18px;
        font-size: 0.7rem;
    }
}

/* Body padding to account for fixed header */
body {
    padding-top: 65px;
}

/* Menu open state */
body.menu-open {
    overflow: hidden;
}

body.menu-open .main-nav {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .header {
        background: var(--white, #fff);
        border-bottom: 2px solid var(--text-color, #333);
    }
    
    .nav-link {
        border: 1px solid transparent;
    }
    
    .nav-link:hover,
    .nav-link.active {
        border-color: var(--primary-color, #ff7a00);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .header,
    .logo,
    .nav-link,
    .cart-icon,
    .mobile-menu-toggle,
    .hamburger-line,
    .main-nav {
        transition: none;
    }
    
    .cart-count {
        animation: none;
    }
    
    .logo:hover,
    .nav-link:hover,
    .cart-icon:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .header {
        position: static;
        background: transparent;
        box-shadow: none;
        border-bottom: 1px solid #000;
    }
    
    .mobile-menu-toggle,
    .cart-icon-wrapper {
        display: none;
    }
    
    body {
        padding-top: 0;
    }
}
