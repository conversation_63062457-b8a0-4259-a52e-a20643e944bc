// Admin Dashboard JavaScript
console.log('Admin script loaded');

// Global variables
let currentSection = 'dashboard';
let isAuthenticated = false;
let sidebarCollapsed = false;

// Function to initialize dashboard functionality after login
function initDashboardFeatures() {
    console.log('Initializing dashboard features...');

    // Bind all the interactive elements
    bindDashboardEvents();

    // Initialize charts
    setTimeout(initCharts, 500);

    console.log('Dashboard features initialized');
}

// Bind all dashboard event listeners
function bindDashboardEvents() {
    console.log('Binding dashboard events...');

    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        console.log('Binding sidebar toggle');
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Navigation links
    const navLinks = document.querySelectorAll('.admin-nav-link');
    console.log(`Found ${navLinks.length} navigation links`);
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });

    // Logout button
    const logoutBtn = document.getElementById('adminLogout');
    if (logoutBtn) {
        console.log('Binding logout button');
        logoutBtn.addEventListener('click', handleLogout);
    }

    // Add menu item button
    const addMenuItemBtn = document.getElementById('addMenuItem');
    if (addMenuItemBtn) {
        console.log('Binding add menu item button');
        addMenuItemBtn.addEventListener('click', showAddMenuItemModal);
    }

    // Modal close buttons
    const modalCloseButtons = document.querySelectorAll('.modal-close, .modal-cancel');
    console.log(`Found ${modalCloseButtons.length} modal close buttons`);
    modalCloseButtons.forEach(btn => {
        btn.addEventListener('click', closeModal);
    });

    // Close modal when clicking outside
    const modals = document.querySelectorAll('.admin-modal');
    console.log(`Found ${modals.length} modals`);
    modals.forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.remove('active');
                console.log('Modal closed by clicking outside');
            }
        });
    });

    // Category filter tabs
    const categoryTabs = document.querySelectorAll('.category-tab');
    console.log(`Found ${categoryTabs.length} category tabs`);
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', handleCategoryFilter);
    });

    // Settings navigation
    const settingsNavLinks = document.querySelectorAll('.settings-nav-link');
    console.log(`Found ${settingsNavLinks.length} settings navigation links`);
    settingsNavLinks.forEach(link => {
        link.addEventListener('click', handleSettingsNavigation);
    });

    // File upload inputs
    const fileInputs = document.querySelectorAll('input[type="file"]');
    console.log(`Found ${fileInputs.length} file inputs`);
    fileInputs.forEach(input => {
        input.addEventListener('change', handleFileUpload);
    });

    // Search inputs
    const searchInputs = document.querySelectorAll('.search-input');
    console.log(`Found ${searchInputs.length} search inputs`);
    searchInputs.forEach(input => {
        input.addEventListener('input', handleSearch);
    });

    // Filter selects
    const filterSelects = document.querySelectorAll('.filter-select');
    console.log(`Found ${filterSelects.length} filter selects`);
    filterSelects.forEach(select => {
        select.addEventListener('change', handleFilter);
    });

    // Action buttons
    bindActionButtons();

    // Pagination
    bindPagination();

    // User menu
    bindUserMenu();

    console.log('All dashboard events bound successfully');
}

// Handle logout
function handleLogout(e) {
    if (e) e.preventDefault();
    console.log('Logging out...');

    // Clear authentication
    localStorage.removeItem('adminAuthToken');
    console.log('Auth token removed from localStorage');

    isAuthenticated = false;

    // Hide dashboard
    const dashboard = document.getElementById('adminDashboard');
    if (dashboard) {
        dashboard.classList.remove('active');
        console.log('Dashboard hidden');
    } else {
        console.error('Dashboard element not found');
    }

    // Show login modal
    const modal = document.getElementById('adminLoginModal');
    if (modal) {
        modal.classList.add('active');
        console.log('Login modal shown');
    } else {
        console.error('Login modal not found');
    }
    
    console.log('Logout complete');
}

// Toggle sidebar
function toggleSidebar(e) {
    if (e) e.preventDefault();
    console.log('Toggling sidebar');
    const sidebar = document.getElementById('adminSidebar');
    const main = document.querySelector('.admin-main');

    if (!sidebar || !main) {
        console.error('Sidebar or main element not found');
        return;
    }

    sidebarCollapsed = !sidebarCollapsed;

    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        main.classList.add('expanded');
        console.log('Sidebar collapsed');
    } else {
        sidebar.classList.remove('collapsed');
        main.classList.remove('expanded');
        console.log('Sidebar expanded');
    }
}

// Handle navigation
function handleNavigation(e) {
    e.preventDefault();
    console.log('Navigation clicked');

    const link = e.currentTarget;
    const section = link.dataset.section;

    if (section) {
        console.log(`Switching to section: ${section}`);
        switchSection(section);

        // Update active nav link
        document.querySelectorAll('.admin-nav-link').forEach(l => l.classList.remove('active'));
        link.classList.add('active');
    } else {
        console.error('No section data found on navigation link');
    }
}

// Switch sections
function switchSection(sectionName) {
    console.log(`Switching to section: ${sectionName}`);

    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionName;
        console.log(`Section ${sectionName} is now active`);
    } else {
        console.error(`Section ${sectionName}-section not found`);
    }
}

// Show add menu item modal
function showAddMenuItemModal(e) {
    if (e) e.preventDefault();
    console.log('Show add menu item modal');
    const modal = document.getElementById('addMenuItemModal');
    if (modal) {
        modal.classList.add('active');
        console.log('Add menu item modal shown');
    } else {
        console.error('Add menu item modal not found');
        alert('Add Menu Item functionality - Modal would open here');
    }
}

// Close modal
function closeModal(e) {
    e.preventDefault();
    console.log('Closing modal');
    const modal = e.currentTarget.closest('.admin-modal');
    if (modal) {
        modal.classList.remove('active');
        console.log('Modal closed');
    } else {
        console.error('Modal not found');
    }
}

// Handle category filter
function handleCategoryFilter(e) {
    e.preventDefault();
    const category = e.target.dataset.category;
    console.log('Category filter:', category);

    if (!category) {
        console.error('No category data found on tab');
        return;
    }

    // Update active tab
    document.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
    e.target.classList.add('active');

    // Filter items (demo functionality)
    console.log(`Filtering menu items by category: ${category}`);

    // In a real application, this would filter the menu items
    // For now, just show a confirmation
    const categoryName = category === 'all' ? 'All Items' :
                        category.charAt(0).toUpperCase() + category.slice(1);
    alert(`Showing ${categoryName} - Filter functionality working!`);
}

// Handle settings navigation
function handleSettingsNavigation(e) {
    e.preventDefault();
    const link = e.currentTarget;
    const targetId = link.getAttribute('href').substring(1);

    console.log('Settings navigation to:', targetId);

    if (!targetId) {
        console.error('No target ID found in settings link');
        return;
    }

    // Update active nav link
    document.querySelectorAll('.settings-nav-link').forEach(l => l.classList.remove('active'));
    link.classList.add('active');

    // Show target panel
    document.querySelectorAll('.settings-panel').forEach(panel => {
        panel.classList.remove('active');
    });

    const targetPanel = document.getElementById(targetId);
    if (targetPanel) {
        targetPanel.classList.add('active');
        console.log(`Settings panel ${targetId} activated`);
    } else {
        console.log(`Settings panel ${targetId} not found - this is normal for demo`);
        alert(`Settings: ${targetId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} - Navigation working!`);
    }
}

// Handle file upload
function handleFileUpload(e) {
    console.log('File upload');
    const file = e.target.files[0];
    if (file) {
        console.log('File selected:', file.name);
        const preview = e.target.parentElement.querySelector('.file-preview');
        if (preview && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 8px;">`;
            };
            reader.readAsDataURL(file);
        }
        alert(`File selected: ${file.name}`);
    }
}

// Handle search
function handleSearch(e) {
    const query = e.target.value.toLowerCase().trim();
    console.log('Search query:', query);

    if (query.length === 0) {
        console.log('Search cleared');
        return;
    }

    if (query.length >= 3) {
        console.log(`Performing search for: ${query}`);
        // In a real application, this would filter the results
        // For now, just show a confirmation after a short delay
        clearTimeout(window.searchTimeout);
        window.searchTimeout = setTimeout(() => {
            alert(`Search results for: "${query}" - Search functionality working!`);
        }, 1000);
    }
}

// Handle filter
function handleFilter(e) {
    const filter = e.target.value;
    const filterName = e.target.options ? e.target.options[e.target.selectedIndex].text : filter;
    console.log('Filter applied:', filter);

    if (filter && filter !== 'all') {
        console.log(`Filtering by: ${filter}`);
        alert(`Filtered by: ${filterName} - Filter functionality working!`);
    } else {
        console.log('Filter cleared');
        alert('Filter cleared - Showing all items');
    }
}

// Bind action buttons
function bindActionButtons() {
    console.log('Binding action buttons');

    // View buttons
    const viewButtons = document.querySelectorAll('.view-btn');
    console.log(`Found ${viewButtons.length} view buttons`);
    viewButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('View action clicked');
            alert('View functionality - This would open a detailed view');
        });
    });

    // Edit buttons
    const editButtons = document.querySelectorAll('.edit-btn');
    console.log(`Found ${editButtons.length} edit buttons`);
    editButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Edit action clicked');
            alert('Edit functionality - This would open an edit form');
        });
    });

    // Delete buttons
    const deleteButtons = document.querySelectorAll('.delete-btn');
    console.log(`Found ${deleteButtons.length} delete buttons`);
    deleteButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Delete action clicked');
            if (confirm('Are you sure you want to delete this item?')) {
                alert('Delete functionality - Item would be deleted');
            }
        });
    });

    // Toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-btn');
    console.log(`Found ${toggleButtons.length} toggle buttons`);
    toggleButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Toggle action clicked');
            const icon = btn.querySelector('i');
            if (icon && icon.classList.contains('fa-eye')) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                btn.title = 'Show Item';
                alert('Item hidden');
            } else if (icon) {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                btn.title = 'Hide Item';
                alert('Item shown');
            }
        });
    });

    // Message buttons
    const messageButtons = document.querySelectorAll('.message-btn');
    console.log(`Found ${messageButtons.length} message buttons`);
    messageButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Message action clicked');
            alert('Message functionality - This would open a messaging interface');
        });
    });

    console.log('Action buttons binding complete');
}

// Bind pagination
function bindPagination() {
    console.log('Binding pagination');

    // Pagination numbers
    document.querySelectorAll('.pagination-number').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Pagination number clicked:', btn.textContent);

            // Update active pagination
            document.querySelectorAll('.pagination-number').forEach(p => p.classList.remove('active'));
            btn.classList.add('active');

            alert(`Loading page ${btn.textContent}`);
        });
    });

    // Previous/Next buttons
    document.querySelectorAll('.pagination-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Pagination button clicked:', btn.textContent);
            alert(`${btn.textContent} page functionality`);
        });
    });
}

// Bind user menu functionality
function bindUserMenu() {
    console.log('Binding user menu');
    const userMenuToggle = document.querySelector('.user-menu-toggle');
    const userMenuDropdown = document.querySelector('.user-menu-dropdown');

    if (userMenuToggle && userMenuDropdown) {
        console.log('User menu elements found, binding events');

        userMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('User menu toggle clicked');

            // Toggle the dropdown
            const isVisible = userMenuDropdown.classList.contains('active') ||
                             userMenuDropdown.style.visibility === 'visible';

            if (isVisible) {
                userMenuDropdown.classList.remove('active');
                userMenuDropdown.style.opacity = '0';
                userMenuDropdown.style.visibility = 'hidden';
                console.log('User menu closed');
            } else {
                userMenuDropdown.classList.add('active');
                userMenuDropdown.style.opacity = '1';
                userMenuDropdown.style.visibility = 'visible';
                console.log('User menu opened');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!userMenuToggle.contains(e.target) && !userMenuDropdown.contains(e.target)) {
                userMenuDropdown.classList.remove('active');
                userMenuDropdown.style.opacity = '0';
                userMenuDropdown.style.visibility = 'hidden';
            }
        });
    } else {
        console.log('User menu elements not found');
        if (!userMenuToggle) console.log('User menu toggle not found');
        if (!userMenuDropdown) console.log('User menu dropdown not found');
    }
}

// Initialize charts
function initCharts() {
    console.log('Initializing charts...');
    // Charts will be initialized when Chart.js is available
    if (typeof Chart !== 'undefined') {
        createSalesChart();
        createCategoriesChart();
        createDemographicsChart();
    } else {
        console.log('Chart.js not loaded yet, skipping chart initialization');
    }
}

// Create sales chart
function createSalesChart() {
    const salesChartEl = document.getElementById('salesChart');
    if (!salesChartEl) return;

    const ctx = salesChartEl.getContext('2d');

    // Sample data
    const data = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Sales (₦)',
            data: [65000, 59000, 80000, 81000, 56000, 95000],
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1
        }]
    };

    new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Create categories chart
function createCategoriesChart() {
    const categoriesChartEl = document.getElementById('categoriesChart');
    if (!categoriesChartEl) return;

    const ctx = categoriesChartEl.getContext('2d');

    // Sample data
    const data = {
        labels: ['Main Dishes', 'Appetizers', 'Desserts', 'Beverages'],
        datasets: [{
            data: [45, 25, 15, 15],
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)'
            ]
        }]
    };

    new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Create demographics chart
function createDemographicsChart() {
    const demographicsChartEl = document.getElementById('demographicsChart');
    if (!demographicsChartEl) return;

    const ctx = demographicsChartEl.getContext('2d');

    // Sample data
    const data = {
        labels: ['18-25', '26-35', '36-45', '46-55', '55+'],
        datasets: [{
            label: 'Customer Age Groups',
            data: [25, 35, 20, 15, 5],
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    };

    new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, checking if dashboard is visible...');

    // Check if dashboard is already visible (user already logged in)
    const dashboard = document.getElementById('adminDashboard');
    if (dashboard && dashboard.classList.contains('active')) {
        console.log('Dashboard is visible, initializing features');
        initDashboardFeatures();
    } else {
        console.log('Dashboard not visible yet, waiting for login');
    }
});

// Also check on window load as fallback
window.addEventListener('load', () => {
    setTimeout(() => {
        const dashboard = document.getElementById('adminDashboard');
        if (dashboard && dashboard.classList.contains('active')) {
            console.log('Dashboard visible on window load, ensuring features are initialized');
            initDashboardFeatures();
        }
    }, 1000);
});
