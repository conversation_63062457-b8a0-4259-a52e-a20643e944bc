
/* Cart Page Styles */

/* Main Layout */
.main-content {
    padding: calc(var(--spacing-xl) * 2) 0;
    min-height: calc(100vh - 200px);
    background-color: var(--background-color);
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Cart Header */
.cart-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    animation: fadeInUp 0.6s ease-out;
}

.section-heading {
    font-size: 2.5rem;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    font-weight: 700;
    line-height: 1.2;
}

/* Checkout Progress */
.checkout-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 600px;
    margin: var(--spacing-xl) auto;
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
    flex: 1;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--border-color);
    color: var(--text-light);
    transition: var(--transition);
    margin-bottom: var(--spacing-xs);
}

.progress-step.active .step-icon {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.step-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.progress-step.active .step-label {
    color: var(--primary-color);
}

.progress-connector {
    flex: 1;
    height: 2px;
    background-color: var(--border-color);
    margin: 0 var(--spacing-sm);
    position: relative;
    top: -25px;
    z-index: 0;
}

.progress-step.active + .progress-connector {
    background-color: var(--primary-color);
}

/* Cart Layout */
.cart-content-wrapper {
    display: grid !important;
    grid-template-columns: 1fr 350px;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
    visibility: visible !important;
    opacity: 1 !important;
}

/* Cart Items Section */
.cart-items-section {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-lg);
    transition: var(--transition);
}

.cart-items-section:hover {
    box-shadow: var(--shadow-md);
}

.cart-items-container {
    min-height: 400px;
}

/* Empty Cart */
.empty-cart-message {
    text-align: center;
    padding: var(--spacing-xl) 0;
    animation: fadeInUp 0.6s ease-out;
}

.empty-cart-illustration {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.empty-cart-message h2 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
}

.empty-cart-message p {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
}

/* Cart Items */
.cart-item {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    position: relative;
    animation: fadeInUp 0.6s ease-out;
}

.cart-item:last-child {
    border-bottom: none;
}

.item-image-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.item-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.cart-item:hover .item-image {
    transform: scale(1.05);
}

.remove-item-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
}

.remove-item-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.item-details {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item-title {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
}

.item-price {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

/* Quantity Controls */
.quantity-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background-color: var(--background-color);
    padding: 4px;
    border-radius: var(--border-radius);
    width: fit-content;
}

.quantity-btn {
    background: none;
    border: none;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-color);
    transition: var(--transition);
}

.quantity-btn:hover {
    color: var(--primary-color);
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-input {
    width: 40px;
    text-align: center;
    border: none;
    background: none;
    font-weight: 500;
    color: var(--text-color);
}

/* Cart Summary */
.cart-summary {
    position: sticky;
    top: 100px;
}

.summary-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-lg);
    transition: var(--transition);
}

.summary-card:hover {
    box-shadow: var(--shadow-md);
}

.summary-heading {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.summary-content {
    margin-bottom: var(--spacing-lg);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    color: var(--text-light);
}

.summary-row.total {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-color);
    font-size: 1.1rem;
}

.checkout-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
}

.checkout-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.keyboard-shortcuts {
    margin-top: var(--spacing-sm);
    text-align: center;
    opacity: 0.7;
    font-size: 0.8rem;
}

.keyboard-shortcuts i {
    margin-right: var(--spacing-xs);
    color: var(--primary-color);
}

.text-muted {
    color: var(--text-light);
}

/* Error States */
.cart-error {
    text-align: center;
    padding: var(--spacing-lg);
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    display: none;
}

.cart-error i {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
}

/* Success States */
.cart-success {
    text-align: center;
    padding: var(--spacing-lg);
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    display: none;
    animation: slideInDown 0.3s ease-out;
}

.cart-success i {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    color: #28a745;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.cart-item.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading-skeleton {
    background: linear-gradient(
        90deg,
        var(--background-color) 25%,
        var(--border-color) 50%,
        var(--background-color) 75%
    );
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
/* Responsive Design */
@media (max-width: var(--desktop)) {
    .cart-content-wrapper {
        grid-template-columns: 1fr 300px;
    }
}

@media (max-width: var(--tablet)) {
    .cart-content-wrapper {
        grid-template-columns: 1fr;
    }

    .cart-summary {
        position: static;
        margin-top: var(--spacing-lg);
    }

    .checkout-progress {
        max-width: 100%;
        padding: 0 var(--spacing-sm);
    }

    .step-icon {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .step-label {
        font-size: 0.8rem;
    }
}

@media (max-width: var(--mobile)) {
    .section-heading {
        font-size: 2rem;
    }

    .cart-item {
        grid-template-columns: 1fr;
    }

    .item-image-container {
        height: 200px;
    }

    .checkout-progress {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .progress-connector {
        width: 2px;
        height: 20px;
        margin: var(--spacing-xs) 0;
    }
}

