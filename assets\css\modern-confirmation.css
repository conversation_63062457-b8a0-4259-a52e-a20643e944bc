/* Modern Confirmation Page Styles */

/* CSS Custom Properties for Confirmation */
:root {
    --confirmation-border-radius: 12px;
    --confirmation-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --confirmation-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --confirmation-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --success-color: #10b981;
    --success-light: rgba(16, 185, 129, 0.1);
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Confirmation Page Layout */
.confirmation-page {
    background: linear-gradient(135deg, var(--success-light) 0%, #f1f5f9 100%);
    min-height: 100vh;
}

.main-content {
    padding: calc(var(--spacing-xl) + 65px) 0 var(--spacing-xl);
    min-height: calc(100vh - 200px);
}

/* Success Animation */
.success-animation {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-xl);
}

.success-checkmark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    stroke-width: 2;
    stroke: var(--success-color);
    stroke-miterlimit: 10;
    box-shadow: inset 0px 0px 0px var(--success-color);
    animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
    position: relative;
}

.success-checkmark .check-icon {
    width: 80px;
    height: 80px;
    position: relative;
    border-radius: 50%;
    box-sizing: content-box;
    border: 4px solid var(--success-color);
}

.success-checkmark .check-icon::before {
    top: 3px;
    left: -2px;
    width: 30px;
    transform-origin: 100% 50%;
    border-radius: 100px 0 0 100px;
}

.success-checkmark .check-icon::after {
    top: 0;
    left: 30px;
    width: 60px;
    transform-origin: 0 50%;
    border-radius: 0 100px 100px 0;
    animation: rotate-circle 4.25s ease-in;
}

.success-checkmark .check-icon::before,
.success-checkmark .check-icon::after {
    content: '';
    height: 100px;
    position: absolute;
    background: var(--white);
    transform: rotate(-45deg);
}

.success-checkmark .icon-line {
    height: 5px;
    background-color: var(--success-color);
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 10;
}

.success-checkmark .icon-line.line-tip {
    top: 46px;
    left: 14px;
    width: 25px;
    transform: rotate(45deg);
    animation: icon-line-tip 0.75s;
}

.success-checkmark .icon-line.line-long {
    top: 38px;
    right: 8px;
    width: 47px;
    transform: rotate(-45deg);
    animation: icon-line-long 0.75s;
}

.success-checkmark .icon-circle {
    top: -4px;
    left: -4px;
    z-index: 10;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    position: absolute;
    box-sizing: content-box;
    border: 4px solid rgba(16, 185, 129, 0.2);
}

.success-checkmark .icon-fix {
    top: 8px;
    width: 5px;
    left: 26px;
    z-index: 1;
    height: 85px;
    position: absolute;
    transform: rotate(-45deg);
    background-color: var(--white);
}

@keyframes rotate-circle {
    0% { transform: rotate(-45deg); }
    5% { transform: rotate(-45deg); }
    12% { transform: rotate(-405deg); }
    100% { transform: rotate(-405deg); }
}

@keyframes icon-line-tip {
    0% { width: 0; left: 1px; top: 19px; }
    54% { width: 0; left: 1px; top: 19px; }
    70% { width: 50px; left: -8px; top: 37px; }
    84% { width: 17px; left: 21px; top: 48px; }
    100% { width: 25px; left: 14px; top: 45px; }
}

@keyframes icon-line-long {
    0% { width: 0; right: 46px; top: 54px; }
    65% { width: 0; right: 46px; top: 54px; }
    84% { width: 55px; right: 0px; top: 35px; }
    100% { width: 47px; right: 8px; top: 38px; }
}

@keyframes fill {
    100% { box-shadow: inset 0px 0px 0px 60px var(--success-color); }
}

@keyframes scale {
    0%, 100% { transform: none; }
    50% { transform: scale3d(1.1, 1.1, 1); }
}

/* Confirmation Header */
.confirmation-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.confirmation-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: var(--spacing-sm);
    animation: fadeInUp 0.6s ease-out 0.5s both;
}

.confirmation-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    animation: fadeInUp 0.6s ease-out 0.7s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress Indicator */
.checkout-progress {
    margin: var(--spacing-xl) 0;
    display: flex;
    justify-content: center;
    animation: fadeInUp 0.6s ease-out 0.9s both;
}

.progress-steps {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    z-index: 2;
}

.progress-step::after {
    content: '';
    position: absolute;
    top: 25px;
    left: 50px;
    width: 60px;
    height: 2px;
    background: var(--success-color);
    z-index: 1;
}

.progress-step:last-child::after {
    display: none;
}

.step-indicator {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--success-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    transition: var(--confirmation-transition);
    position: relative;
    z-index: 2;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
}

.step-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--success-color);
    text-align: center;
}

/* Confirmation Layout */
.confirmation-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-xl);
    align-items: start;
    animation: fadeInUp 0.6s ease-out 1.1s both;
}

/* Order Details Section */
.order-details-section {
    background: var(--white);
    border-radius: var(--confirmation-border-radius);
    box-shadow: var(--confirmation-shadow);
    transition: var(--confirmation-transition);
}

.order-details-section:hover {
    box-shadow: var(--confirmation-shadow-hover);
}

.details-card {
    padding: var(--spacing-xl);
}

.section-heading {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--background-color);
}

/* Order Info */
.order-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.info-value {
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 600;
}

.order-number {
    color: var(--primary-color);
    font-family: monospace;
    font-size: 1.1rem;
}

.order-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.confirmed {
    background: var(--success-color);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

/* Order Items */
.order-items {
    margin-bottom: var(--spacing-xl);
}

.items-heading {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.order-item {
    display: grid;
    grid-template-columns: 60px 1fr auto auto;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--confirmation-border-radius);
    align-items: center;
}

.item-image {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    object-fit: cover;
    background: var(--background-color);
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.item-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}

.item-description {
    font-size: 0.8rem;
    color: var(--text-light);
    line-height: 1.3;
}

.item-quantity {
    color: var(--text-light);
    font-weight: 500;
    text-align: center;
}

.item-price {
    font-weight: 600;
    color: var(--primary-color);
    text-align: right;
}

/* Order Totals */
.order-totals {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--confirmation-border-radius);
}

.total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 0.95rem;
}

.total-line.final-total {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    padding-top: var(--spacing-sm);
    border-top: 2px solid var(--border-color);
    margin-top: var(--spacing-md);
}

.total-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-md) 0;
}

/* Customer and Delivery Info */
.customer-info,
.delivery-info {
    margin-bottom: var(--spacing-lg);
}

.info-heading {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.delivery-method {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--confirmation-border-radius);
}

.method-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.method-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.method-type {
    font-weight: 600;
    color: var(--text-color);
}

.delivery-address {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Tracking Section */
.tracking-section {
    position: sticky;
    top: calc(65px + var(--spacing-lg));
}

.tracking-card {
    background: var(--white);
    border-radius: var(--confirmation-border-radius);
    box-shadow: var(--confirmation-shadow);
    padding: var(--spacing-xl);
    transition: var(--confirmation-transition);
}

.tracking-card:hover {
    box-shadow: var(--confirmation-shadow-hover);
}

/* Status Timeline */
.status-timeline {
    margin-bottom: var(--spacing-xl);
}

.timeline-item {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 50px;
    width: 2px;
    height: calc(100% + var(--spacing-md));
    background: var(--border-color);
}

.timeline-item.completed::after {
    background: var(--success-color);
}

.timeline-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: var(--confirmation-transition);
    flex-shrink: 0;
    position: relative;
    z-index: 2;
}

.timeline-item.completed .timeline-icon {
    background: var(--success-color);
    color: var(--white);
}

.timeline-content {
    flex: 1;
    padding-top: 4px;
}

.timeline-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
}

.timeline-content p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
    line-height: 1.4;
}

.timeline-time {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

.timeline-item.completed .timeline-time {
    color: var(--success-color);
}

/* Support Section */
.support-section {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-color);
    border-radius: var(--confirmation-border-radius);
}

.support-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
}

.support-section p {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.support-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Order Actions */
.order-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--confirmation-border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--confirmation-transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--confirmation-shadow);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #34495e;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Thank You Section */
.thank-you-section {
    margin-top: var(--spacing-xl);
    animation: fadeInUp 0.6s ease-out 1.3s both;
}

.thank-you-card {
    background: var(--white);
    border-radius: var(--confirmation-border-radius);
    box-shadow: var(--confirmation-shadow);
    padding: var(--spacing-xl);
    text-align: center;
}

.thank-you-card h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
}

.thank-you-card p {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Social Share */
.social-share {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.social-share span {
    font-weight: 500;
    color: var(--text-color);
}

.share-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

.share-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--confirmation-transition);
    font-size: 1.1rem;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--confirmation-shadow);
}

.share-btn.facebook {
    background: #1877f2;
}

.share-btn.twitter {
    background: #1da1f2;
}

.share-btn.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.share-btn.whatsapp {
    background: #25d366;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: calc(65px + var(--spacing-md));
    right: var(--spacing-md);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--white);
    border-radius: var(--confirmation-border-radius);
    box-shadow: var(--confirmation-shadow-hover);
    padding: var(--spacing-md);
    max-width: 300px;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    animation: slideInRight 0.3s ease-out;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid #ef4444;
}

.toast.warning {
    border-left: 4px solid #f59e0b;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .confirmation-layout {
        grid-template-columns: 1fr 350px;
    }
}

@media (max-width: 768px) {
    .confirmation-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .tracking-section {
        position: static;
        order: -1;
    }

    .order-info {
        grid-template-columns: 1fr;
    }

    .order-item {
        grid-template-columns: 50px 1fr auto;
    }

    .item-quantity {
        display: none;
    }

    .support-actions {
        flex-direction: column;
    }

    .progress-steps {
        gap: var(--spacing-md);
    }

    .progress-step::after {
        width: 40px;
        left: 40px;
    }

    .step-indicator {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .step-label {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: calc(var(--spacing-lg) + 65px) 0 var(--spacing-lg);
    }

    .confirmation-title {
        font-size: 2rem;
    }

    .details-card,
    .tracking-card,
    .thank-you-card {
        padding: var(--spacing-lg);
    }

    .progress-steps {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .progress-step::after {
        top: 40px;
        left: 50%;
        transform: translateX(-50%);
        width: 2px;
        height: 20px;
    }

    .success-checkmark {
        width: 60px;
        height: 60px;
    }

    .success-checkmark .check-icon {
        width: 60px;
        height: 60px;
    }

    .order-item {
        grid-template-columns: 1fr auto;
        text-align: left;
    }

    .item-image {
        display: none;
    }

    .share-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Print styles */
@media print {
    .confirmation-page {
        background: white;
    }

    .header,
    .tracking-section,
    .thank-you-section,
    .toast-container,
    .order-actions,
    .support-section {
        display: none;
    }

    .confirmation-layout {
        grid-template-columns: 1fr;
    }

    .details-card {
        box-shadow: none;
        border: 1px solid #000;
    }

    .success-animation {
        display: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .details-card,
    .tracking-card,
    .thank-you-card {
        border: 2px solid var(--text-color);
    }

    .btn {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .success-checkmark {
        animation: none;
    }

    .confirmation-title,
    .confirmation-subtitle,
    .checkout-progress,
    .confirmation-layout,
    .thank-you-section {
        animation: none;
    }
}
