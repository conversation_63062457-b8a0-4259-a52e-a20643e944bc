/* Modern Contact Page Styles */

/* CSS Custom Properties for Contact */
:root {
    --contact-border-radius: var(--radius-xl);
    --contact-shadow: var(--shadow-md);
    --contact-shadow-hover: var(--shadow-lg);
    --contact-transition: var(--transition-default);
    --contact-animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --input-focus-color: rgba(255, 122, 0, 0.1);
}

/* Hero Section */
.contact-hero {
    background: linear-gradient(135deg, var(--primary-alpha), var(--secondary-alpha));
    padding: var(--spacing-20) 0 var(--spacing-16);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,122,0,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-4);
    color: var(--text-color);
    font-family: var(--font-family-secondary);
    position: relative;
    z-index: 1;
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    font-weight: var(--font-weight-medium);
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;
    line-height: var(--line-height-relaxed);
}

/* Main Content Layout */
.main-content {
    padding: var(--spacing-16) 0;
    background: var(--background-color);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    margin-bottom: var(--spacing-16);
}

@media (min-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr 1.2fr;
        gap: var(--spacing-16);
    }
}

@media (min-width: 1024px) {
    .contact-grid {
        grid-template-columns: 1fr 1.5fr;
        gap: var(--spacing-20);
    }
}

/* Contact Information Section */
.contact-info-section {
    background: var(--white);
    padding: var(--spacing-12);
    border-radius: var(--contact-border-radius);
    box-shadow: var(--contact-shadow);
    height: fit-content;
}

.contact-info-section h2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: var(--spacing-8);
    font-family: var(--font-family-secondary);
}

.info-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
}

@media (min-width: 640px) {
    .info-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .info-cards {
        grid-template-columns: 1fr;
    }
}

.info-card {
    background: var(--background-light);
    padding: var(--spacing-6);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: var(--contact-transition);
    position: relative;
    overflow: hidden;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: var(--contact-transition);
    transform-origin: bottom;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--contact-shadow-hover);
    border-color: var(--primary-light);
}

.info-card:hover::before {
    transform: scaleY(1);
}

.icon-wrapper {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-4);
    box-shadow: var(--shadow-sm);
    transition: var(--contact-transition);
}

.info-card:hover .icon-wrapper {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.icon-wrapper i {
    color: var(--white);
    font-size: var(--font-size-lg);
}

.info-card h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-2);
    font-family: var(--font-family-secondary);
}

.info-card p {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
    margin: 0;
}

.info-card a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: var(--contact-transition);
}

.info-card a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Contact Form Section */
.contact-form-section {
    background: var(--white);
    padding: var(--spacing-12);
    border-radius: var(--contact-border-radius);
    box-shadow: var(--contact-shadow);
    position: relative;
    overflow: hidden;
}

.contact-form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.contact-form-section h2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: var(--spacing-8);
    font-family: var(--font-family-secondary);
}

/* Form Styling */
.contact-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.form-group {
    position: relative;
}

.form-group label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: var(--spacing-2);
}

.form-group label.required::after {
    content: ' *';
    color: var(--error-color);
}

.form-input {
    width: 100%;
    padding: var(--spacing-4) var(--spacing-4);
    border: 2px solid var(--border-color);
    border-radius: var(--contact-border-radius);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    transition: var(--contact-transition);
    background: var(--white);
    color: var(--text-color);
    resize: vertical;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--input-focus-color);
}

.form-input:invalid:not(:focus):not(:placeholder-shown) {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px var(--error-light);
}

.form-input:valid:not(:focus):not(:placeholder-shown) {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px var(--success-light);
}

.form-input::placeholder {
    color: var(--text-light);
    opacity: 0.7;
}

/* Textarea specific styling */
.form-input[type="textarea"],
textarea.form-input {
    min-height: 120px;
    resize: vertical;
    font-family: var(--font-family-primary);
}

/* Error Messages */
.error-message {
    color: var(--error-color);
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-1);
    display: none;
    font-weight: var(--font-weight-medium);
    animation: slideInDown 0.3s ease-out;
}

.error-message.show {
    display: block;
}

.form-group.error .form-input {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px var(--error-light);
}

.form-group.success .form-input {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px var(--success-light);
}

/* Submit Button */
.submit-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: var(--spacing-4) var(--spacing-8);
    border: none;
    border-radius: var(--contact-border-radius);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    transition: var(--contact-transition);
    position: relative;
    overflow: hidden;
    min-height: 48px;
    box-shadow: var(--shadow-sm);
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-sm);
}

.submit-btn:disabled:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    transform: none;
}

/* Loading state */
.submit-btn.loading {
    pointer-events: none;
}

.submit-btn.loading .btn-text {
    opacity: 0.7;
}

.submit-btn.loading .btn-icon {
    animation: spin 1s linear infinite;
}

/* Button icon */
.btn-icon {
    font-size: var(--font-size-sm);
    transition: var(--contact-transition);
}

/* Map Section */
.map-section {
    margin-top: var(--spacing-16);
}

.map-wrapper {
    border-radius: var(--contact-border-radius);
    overflow: hidden;
    box-shadow: var(--contact-shadow);
    background: var(--white);
    position: relative;
    height: 400px;
}

.map-wrapper iframe {
    display: block;
    width: 100%;
    height: 100%;
    border: none;
}

.map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--background-light);
    color: var(--text-light);
    text-align: center;
    padding: var(--spacing-8);
}

.map-placeholder i {
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.map-placeholder p {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    margin: 0;
}

.map-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--error-light);
    color: var(--error-color);
    text-align: center;
    padding: var(--spacing-8);
}

.map-error i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-4);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-4);
    right: var(--spacing-4);
    z-index: var(--z-tooltip);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    pointer-events: none;
}

.toast {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-4) var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    opacity: 0;
    transition: var(--transition-slow);
    pointer-events: auto;
    border-left: 4px solid var(--primary-color);
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--error-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast.info {
    border-left-color: var(--info-color);
}

.toast-icon {
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--error-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.info .toast-icon {
    color: var(--info-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    color: var(--text-color);
    margin-bottom: var(--spacing-1);
}

.toast-message {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    line-height: var(--line-height-snug);
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--radius-sm);
    transition: var(--contact-transition);
    flex-shrink: 0;
}

.toast-close:hover {
    background: var(--background-light);
    color: var(--text-color);
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Loading Animation */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .contact-hero {
        padding: var(--spacing-16) 0 var(--spacing-12);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .contact-info-section,
    .contact-form-section {
        padding: var(--spacing-10);
    }
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
    }

    .info-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
    }

    .contact-info-section,
    .contact-form-section {
        padding: var(--spacing-8);
    }

    .map-wrapper {
        height: 300px;
    }

    .toast {
        min-width: 280px;
        max-width: calc(100vw - var(--spacing-8));
    }
}

@media (max-width: 640px) {
    .info-cards {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .contact-hero {
        padding: var(--spacing-12) 0 var(--spacing-8);
    }

    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .contact-info-section,
    .contact-form-section {
        padding: var(--spacing-6);
    }

    .main-content {
        padding: var(--spacing-12) 0;
    }

    .contact-grid {
        gap: var(--spacing-6);
    }

    .info-card {
        padding: var(--spacing-4);
    }

    .icon-wrapper {
        width: 48px;
        height: 48px;
    }

    .map-wrapper {
        height: 250px;
    }

    .toast-container {
        top: var(--spacing-2);
        right: var(--spacing-2);
        left: var(--spacing-2);
    }

    .toast {
        min-width: auto;
        max-width: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .form-input,
    .info-card,
    .contact-form-section,
    .contact-info-section {
        border-width: 2px;
    }

    .submit-btn {
        border: 2px solid var(--primary-color);
    }

    .toast {
        border: 2px solid var(--text-color);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .contact-hero::before,
    .info-card,
    .form-input,
    .submit-btn,
    .toast,
    .icon-wrapper {
        transition: none;
    }

    .submit-btn::before {
        display: none;
    }

    .loading-spinner,
    .btn-icon {
        animation: none;
    }

    .info-card:hover,
    .submit-btn:hover {
        transform: none;
    }
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Map Loading and Error States */
.map-loading {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    font-style: italic;
    margin-top: var(--spacing-2);
}

.map-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--error-light);
    color: var(--error-color);
    text-align: center;
    padding: var(--spacing-8);
    border-radius: var(--contact-border-radius);
}

.map-error i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-4);
}

.map-error p {
    margin-bottom: var(--spacing-4);
    font-weight: var(--font-weight-medium);
}

.btn-small {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
}

/* Form Enhancement */
.form-group.ready .form-input {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px var(--success-light);
}

.submit-btn.ready {
    background: linear-gradient(135deg, var(--success-color), #059669);
    animation: pulse 2s infinite;
}

.submit-btn.ready:hover {
    background: linear-gradient(135deg, #059669, var(--success-color));
}

/* Focus Indicators */
.form-input:focus,
.submit-btn:focus,
.toast-close:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Enhanced Accessibility */
@media (prefers-reduced-motion: no-preference) {
    .info-card {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
    }

    .info-card:nth-child(1) { animation-delay: 0.1s; }
    .info-card:nth-child(2) { animation-delay: 0.2s; }
    .info-card:nth-child(3) { animation-delay: 0.3s; }
    .info-card:nth-child(4) { animation-delay: 0.4s; }
}

/* Print styles */
@media print {
    .contact-hero {
        background: none;
        padding: var(--spacing-4) 0;
    }

    .contact-hero::before {
        display: none;
    }

    .submit-btn,
    .map-section,
    .toast-container,
    .btn-icon {
        display: none;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .info-card,
    .contact-form-section,
    .contact-info-section {
        box-shadow: none;
        border: 1px solid var(--border-color);
        break-inside: avoid;
    }

    .form-input {
        border: 1px solid var(--border-color);
        background: transparent;
    }

    .error-message {
        display: none;
    }
}
