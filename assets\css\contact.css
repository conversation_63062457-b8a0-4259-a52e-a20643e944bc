/* Contact Page Styles */
.contact-hero {
    background-color: #f8f9fa;
    padding: 4rem 0;
    text-align: center;
    position: relative;
}

.hero-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
}

/* Contact Grid Layout */
.contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin: 3rem 0;
}

@media (min-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr 1fr;
    }
}

/* Info Cards */
.info-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.info-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.icon-wrapper {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.icon-wrapper i {
    color: var(--white);
    font-size: 1.2rem;
}

.info-card h3 {
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
}

/* Contact Form */
.form-wrapper {
    background: var(--white);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
}

.floating-label {
    position: relative;
    margin-bottom: 2rem;
}

.floating-label input,
.floating-label textarea {
    width: 100%;
    padding: 0.8rem;
    border: none;
    border-bottom: 2px solid var(--border-color);
    background: transparent;
    transition: var(--transition);
}

.floating-label label {
    position: absolute;
    left: 0;
    top: 0.8rem;
    transition: var(--transition);
    color: var(--text-light);
}

.floating-label input:focus ~ label,
.floating-label input:not(:placeholder-shown) ~ label,
.floating-label textarea:focus ~ label,
.floating-label textarea:not(:placeholder-shown) ~ label {
    top: -1.rem;
    font-size: 0.8rem;
    color: var(--primary-color);
}

.input-focus-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.floating-label input:focus ~ .input-focus-border,
.floating-label textarea:focus ~ .input-focus-border {
    width: 100%;
}

.submit-btn {
    background: var(--primary-color);
    color: var(--white);
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.submit-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Map Section */
.map-section {
    margin: 3rem 0;
}

.map-wrapper {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.map-wrapper iframe {
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .info-cards {
        grid-template-columns: 1fr;
    }

    .hero-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .form-wrapper {
        padding: 1.5rem;
    }

    .contact-hero {
        padding: 4rem 0;
    }
}


/* Add to your contact.css */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform: translateY(-20px);
  }
  
  .notification.show {
    opacity: 1;
    transform: translateY(0);
  }
  
  .notification.success {
    background-color: #5cb85c; /* Green */
  }
  
  .notification.error {
    background-color: #d9534f; /* Red */
  }
  
  .notification.info {
    background-color: #5bc0de; /* Blue */
  }
