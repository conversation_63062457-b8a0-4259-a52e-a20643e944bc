/**
 * Modern Header Component
 * Handles header functionality across all pages including navigation,
 * cart count updates, mobile menu, and scroll effects
 */

export class ModernHeader {
    constructor() {
        this.elements = {};
        this.state = {
            isMenuOpen: false,
            cartCount: 0,
            isScrolled: false
        };
        
        this.init();
    }

    /**
     * Initialize the header component
     */
    init() {
        this.cacheElements();
        this.bindEvents();
        this.updateCartCount();
        this.setActiveNavLink();
        this.handleScrollEffect();
    }

    /**
     * Cache DOM elements
     */
    cacheElements() {
        this.elements = {
            header: document.querySelector('.header'),
            mobileMenuToggle: document.querySelector('.mobile-menu-toggle'),
            mainNav: document.querySelector('.main-nav'),
            navList: document.querySelector('.nav-list'),
            navLinks: document.querySelectorAll('.nav-link'),
            cartIcon: document.querySelector('.cart-icon'),
            cartCount: document.querySelector('.cart-count'),
            skipLink: document.querySelector('.skip-link')
        };
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Mobile menu toggle
        if (this.elements.mobileMenuToggle) {
            this.elements.mobileMenuToggle.addEventListener('click', this.toggleMobileMenu.bind(this));
        }

        // Cart icon click
        if (this.elements.cartIcon) {
            this.elements.cartIcon.addEventListener('click', this.handleCartClick.bind(this));
        }

        // Navigation link clicks
        this.elements.navLinks.forEach(link => {
            link.addEventListener('click', this.handleNavClick.bind(this));
        });

        // Scroll effect
        window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 100));

        // Resize handler
        window.addEventListener('resize', this.throttle(this.handleResize.bind(this), 250));

        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));

        // Cart update events
        window.addEventListener('cartUpdated', this.handleCartUpdate.bind(this));
        window.addEventListener('storage', this.handleStorageChange.bind(this));

        // Close mobile menu on outside click
        document.addEventListener('click', this.handleOutsideClick.bind(this));
    }

    /**
     * Toggle mobile menu
     */
    toggleMobileMenu() {
        this.state.isMenuOpen = !this.state.isMenuOpen;
        
        // Update ARIA attributes
        this.elements.mobileMenuToggle.setAttribute('aria-expanded', this.state.isMenuOpen);
        
        // Toggle classes
        this.elements.mainNav.classList.toggle('active', this.state.isMenuOpen);
        document.body.classList.toggle('menu-open', this.state.isMenuOpen);
        
        // Focus management
        if (this.state.isMenuOpen) {
            // Focus first nav link when menu opens
            const firstNavLink = this.elements.navList.querySelector('.nav-link');
            if (firstNavLink) {
                setTimeout(() => firstNavLink.focus(), 100);
            }
        }

        // Analytics tracking
        this.trackEvent('mobile_menu_toggle', {
            action: this.state.isMenuOpen ? 'open' : 'close'
        });
    }

    /**
     * Handle cart icon click
     */
    handleCartClick(e) {
        e.preventDefault();
        
        // Navigate to modern cart page
        window.location.href = 'modern-cart.html';
        
        // Analytics tracking
        this.trackEvent('cart_icon_click', {
            cart_count: this.state.cartCount,
            current_page: window.location.pathname
        });
    }

    /**
     * Handle navigation link clicks
     */
    handleNavClick(e) {
        const link = e.currentTarget;
        const href = link.getAttribute('href');
        
        // Update navigation links to point to modern pages
        if (href === 'cart.html') {
            e.preventDefault();
            window.location.href = 'modern-cart.html';
        }
        
        // Close mobile menu on navigation
        if (this.state.isMenuOpen) {
            this.toggleMobileMenu();
        }
        
        // Analytics tracking
        this.trackEvent('navigation_click', {
            link_text: link.textContent.trim(),
            link_href: href,
            from_mobile_menu: this.state.isMenuOpen
        });
    }

    /**
     * Handle scroll effects
     */
    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const shouldBeScrolled = scrollTop > 10;
        
        if (shouldBeScrolled !== this.state.isScrolled) {
            this.state.isScrolled = shouldBeScrolled;
            this.elements.header.classList.toggle('scrolled', shouldBeScrolled);
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Close mobile menu on desktop resize
        if (window.innerWidth > 768 && this.state.isMenuOpen) {
            this.toggleMobileMenu();
        }
    }

    /**
     * Handle keyboard navigation
     */
    handleKeyboardNavigation(e) {
        // Escape key closes mobile menu
        if (e.key === 'Escape' && this.state.isMenuOpen) {
            this.toggleMobileMenu();
            this.elements.mobileMenuToggle.focus();
        }
        
        // Enter/Space on mobile menu toggle
        if ((e.key === 'Enter' || e.key === ' ') && e.target === this.elements.mobileMenuToggle) {
            e.preventDefault();
            this.toggleMobileMenu();
        }
    }

    /**
     * Handle outside clicks to close mobile menu
     */
    handleOutsideClick(e) {
        if (this.state.isMenuOpen && 
            !this.elements.mainNav.contains(e.target) && 
            !this.elements.mobileMenuToggle.contains(e.target)) {
            this.toggleMobileMenu();
        }
    }

    /**
     * Handle cart updates
     */
    handleCartUpdate(e) {
        this.updateCartCount();
    }

    /**
     * Handle localStorage changes (for cart sync across tabs)
     */
    handleStorageChange(e) {
        if (e.key === 'cartItems') {
            this.updateCartCount();
        }
    }

    /**
     * Update cart count display
     */
    updateCartCount() {
        try {
            // Get cart items from localStorage
            const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
            const totalCount = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
            
            this.state.cartCount = totalCount;
            
            if (this.elements.cartCount) {
                this.elements.cartCount.textContent = totalCount;
                
                // Show/hide count badge
                if (totalCount > 0) {
                    this.elements.cartCount.style.display = 'flex';
                } else {
                    this.elements.cartCount.style.display = 'none';
                }
            }
            
            // Update cart icon accessibility
            if (this.elements.cartIcon) {
                const ariaLabel = totalCount > 0 
                    ? `View cart with ${totalCount} item${totalCount !== 1 ? 's' : ''}`
                    : 'View empty cart';
                this.elements.cartIcon.setAttribute('aria-label', ariaLabel);
            }
            
        } catch (error) {
            console.warn('Failed to update cart count:', error);
        }
    }

    /**
     * Set active navigation link based on current page
     */
    setActiveNavLink() {
        const currentPath = window.location.pathname;
        const currentPage = currentPath.split('/').pop() || 'index.html';
        
        // Map modern pages to their navigation equivalents
        const pageMap = {
            'modern-cart.html': 'cart.html',
            'modern-checkout.html': 'cart.html',
            'modern-confirmation.html': 'cart.html'
        };
        
        const navPage = pageMap[currentPage] || currentPage;
        
        // Remove active class from all links
        this.elements.navLinks.forEach(link => {
            link.classList.remove('active');
            link.removeAttribute('aria-current');
        });
        
        // Add active class to current page link
        const activeLink = Array.from(this.elements.navLinks).find(link => {
            const href = link.getAttribute('href');
            return href === navPage || 
                   (navPage === 'index.html' && (href === '/' || href === 'index.html'));
        });
        
        if (activeLink) {
            activeLink.classList.add('active');
            activeLink.setAttribute('aria-current', 'page');
        }
    }

    /**
     * Handle scroll effect initialization
     */
    handleScrollEffect() {
        // Initial scroll check
        this.handleScroll();
        
        // Smooth scroll for skip link
        if (this.elements.skipLink) {
            this.elements.skipLink.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector('#main-content');
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                    target.focus();
                }
            });
        }
    }

    /**
     * Update navigation links to point to modern pages
     */
    updateNavigationLinks() {
        this.elements.navLinks.forEach(link => {
            const href = link.getAttribute('href');
            
            // Update cart/order links to point to modern cart
            if (href === 'cart.html') {
                link.setAttribute('href', 'modern-cart.html');
            }
        });
    }

    /**
     * Throttle function for performance
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Track analytics events
     */
    trackEvent(eventName, properties = {}) {
        // Check if analytics is available and user has consented
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, properties);
        }
        
        // Custom analytics tracking
        if (window.Analytics && window.Analytics.consent) {
            window.Analytics.track(eventName, properties);
        }
    }

    /**
     * Get current cart count
     */
    getCartCount() {
        return this.state.cartCount;
    }

    /**
     * Manually trigger cart count update
     */
    refreshCartCount() {
        this.updateCartCount();
    }

    /**
     * Check if mobile menu is open
     */
    isMobileMenuOpen() {
        return this.state.isMenuOpen;
    }

    /**
     * Programmatically close mobile menu
     */
    closeMobileMenu() {
        if (this.state.isMenuOpen) {
            this.toggleMobileMenu();
        }
    }

    /**
     * Destroy the header component
     */
    destroy() {
        // Remove event listeners
        window.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('keydown', this.handleKeyboardNavigation);
        document.removeEventListener('click', this.handleOutsideClick);
        window.removeEventListener('cartUpdated', this.handleCartUpdate);
        window.removeEventListener('storage', this.handleStorageChange);
        
        // Remove classes
        document.body.classList.remove('menu-open');
        this.elements.mainNav.classList.remove('active');
    }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modernHeader = new ModernHeader();
});

// Export for manual initialization
export default ModernHeader;
