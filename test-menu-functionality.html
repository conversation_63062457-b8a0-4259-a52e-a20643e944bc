<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .test-result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <h1>Menu Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Quick View Button Click</h2>
        <button class="test-button" onclick="testQuickView()">Test Quick View</button>
        <div id="quickview-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Add to Cart Button Click</h2>
        <button class="test-button" onclick="testAddToCart()">Test Add to Cart</button>
        <div id="addtocart-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Cart Count Update</h2>
        <button class="test-button" onclick="testCartCount()">Test Cart Count</button>
        <div id="cartcount-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Modal Functionality</h2>
        <button class="test-button" onclick="testModal()">Test Modal</button>
        <div id="modal-result" class="test-result"></div>
    </div>

    <script>
        function testQuickView() {
            const result = document.getElementById('quickview-result');
            try {
                // Simulate clicking a quick view button
                const menuContent = parent.document.querySelector('.menu-content');
                if (menuContent) {
                    const quickViewBtn = parent.document.querySelector('.quick-view-btn');
                    if (quickViewBtn) {
                        result.innerHTML = '✓ Quick View button found';
                        result.className = 'test-result success';
                        
                        // Test click event
                        quickViewBtn.click();
                        setTimeout(() => {
                            const modal = parent.document.getElementById('itemDetailModal');
                            if (modal && modal.style.display === 'block') {
                                result.innerHTML += '<br>✓ Modal opened successfully';
                            } else {
                                result.innerHTML += '<br>✗ Modal did not open';
                                result.className = 'test-result error';
                            }
                        }, 100);
                    } else {
                        result.innerHTML = '✗ Quick View button not found';
                        result.className = 'test-result error';
                    }
                } else {
                    result.innerHTML = '✗ Menu content not found';
                    result.className = 'test-result error';
                }
            } catch (error) {
                result.innerHTML = '✗ Error: ' + error.message;
                result.className = 'test-result error';
            }
        }
        
        function testAddToCart() {
            const result = document.getElementById('addtocart-result');
            try {
                const addToCartBtn = parent.document.querySelector('.add-to-cart-btn');
                if (addToCartBtn) {
                    result.innerHTML = '✓ Add to Cart button found';
                    result.className = 'test-result success';
                    
                    // Get initial cart count
                    const initialCount = parent.localStorage.getItem('cartItems');
                    const initialItems = initialCount ? JSON.parse(initialCount).length : 0;
                    
                    // Simulate click
                    addToCartBtn.click();
                    
                    setTimeout(() => {
                        const newCount = parent.localStorage.getItem('cartItems');
                        const newItems = newCount ? JSON.parse(newCount).length : 0;
                        
                        if (newItems > initialItems) {
                            result.innerHTML += '<br>✓ Item added to cart successfully';
                        } else {
                            result.innerHTML += '<br>✗ Item was not added to cart';
                            result.className = 'test-result error';
                        }
                    }, 100);
                } else {
                    result.innerHTML = '✗ Add to Cart button not found';
                    result.className = 'test-result error';
                }
            } catch (error) {
                result.innerHTML = '✗ Error: ' + error.message;
                result.className = 'test-result error';
            }
        }
        
        function testCartCount() {
            const result = document.getElementById('cartcount-result');
            try {
                const cartCount = parent.document.querySelector('.cart-count');
                if (cartCount) {
                    result.innerHTML = '✓ Cart count element found: ' + cartCount.textContent;
                    result.className = 'test-result success';
                } else {
                    result.innerHTML = '✗ Cart count element not found';
                    result.className = 'test-result error';
                }
            } catch (error) {
                result.innerHTML = '✗ Error: ' + error.message;
                result.className = 'test-result error';
            }
        }
        
        function testModal() {
            const result = document.getElementById('modal-result');
            try {
                const modal = parent.document.getElementById('itemDetailModal');
                if (modal) {
                    result.innerHTML = '✓ Modal element found';
                    result.className = 'test-result success';
                    
                    // Test modal elements
                    const modalTitle = parent.document.getElementById('modalTitle');
                    const modalImage = parent.document.getElementById('modalImage');
                    const modalPrice = parent.document.getElementById('modalPrice');
                    
                    if (modalTitle && modalImage && modalPrice) {
                        result.innerHTML += '<br>✓ All modal elements found';
                    } else {
                        result.innerHTML += '<br>✗ Some modal elements missing';
                        result.className = 'test-result error';
                    }
                } else {
                    result.innerHTML = '✗ Modal element not found';
                    result.className = 'test-result error';
                }
            } catch (error) {
                result.innerHTML = '✗ Error: ' + error.message;
                result.className = 'test-result error';
            }
        }
    </script>
</body>
</html>
