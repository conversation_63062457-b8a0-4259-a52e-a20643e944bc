import '@testing-library/jest-dom';
import 'jest-extended';

// Mock localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.localStorage = localStorageMock;

// Mock fetch API
global.fetch = jest.fn();

// Mock performance API
global.performance = {
    now: jest.fn(() => Date.now())
};

// Clean up after each test
afterEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
});