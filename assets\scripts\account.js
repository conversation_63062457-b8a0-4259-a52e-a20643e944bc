document.addEventListener('DOMContentLoaded', () => {
    const switchBtns = document.querySelectorAll('.switch-btn');
    const forms = document.querySelectorAll('.auth-form');
    const switchIndicator = document.querySelector('.switch-indicator');
    const loginForm = document.getElementById('loginForm');
    const signupForm = document.getElementById('signupForm');

    switchBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons and forms
            switchBtns.forEach(b => b.classList.remove('active'));
            forms.forEach(f => f.classList.remove('active'));

            // Add active class to clicked button and corresponding form
            btn.classList.add('active');
            const formId = btn.dataset.form;
            const targetForm = document.getElementById(`${formId}Form`);
            if (targetForm) {
                targetForm.classList.add('active');
            }

            // Move the switch indicator
            if (formId === 'signup') {
                switchIndicator.style.transform = 'translateX(100%)';
            } else {
                switchIndicator.style.transform = 'translateX(0)';
            }
        });
    });

    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = FormValidator.validateForm(loginForm);
            if (!formData) return;

            try {
                const password = formData.get('password');
                const hashedPassword = await Security.hashPassword(password);
                
                // Replace plain password with hashed version
                formData.set('password', hashedPassword);

                // Add nonce for CSRF protection
                formData.append('nonce', Security.generateNonce());

                const response = await api.post('/api/auth/login', formData);
                
                // Handle successful login
                if (response.token) {
                    // Store token in HttpOnly cookie (handled by server)
                    window.location.href = '/dashboard';
                }
            } catch (error) {
                Alert.show('Invalid credentials', 'error');
            }
        });
    }

    // Implement rate limiting for login attempts
    let loginAttempts = 0;
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes
    
    function checkLoginAttempts() {
        if (loginAttempts >= MAX_LOGIN_ATTEMPTS) {
            const lockoutEnd = parseInt(localStorage.getItem('loginLockoutEnd') || '0');
            if (Date.now() < lockoutEnd) {
                Alert.show('Too many login attempts. Please try again later.', 'error');
                return false;
            }
            // Reset after lockout period
            loginAttempts = 0;
            localStorage.removeItem('loginLockoutEnd');
        }
        return true;
    }

    function handleFailedLogin() {
        loginAttempts++;
        if (loginAttempts >= MAX_LOGIN_ATTEMPTS) {
            localStorage.setItem('loginLockoutEnd', String(Date.now() + LOCKOUT_TIME));
        }
    }
});




// Inside account.js
document.querySelectorAll('.toggle-password').forEach(button => {
    button.addEventListener('click', function() {
        const passwordInput = this.previousElementSibling;
        const icon = this.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
});
