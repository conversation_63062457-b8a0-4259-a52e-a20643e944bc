describe('Checkout Flow', () => {
    beforeEach(() => {
        cy.visit('/checkout.html');
        // Seed cart with test data
        cy.window().then((win) => {
            win.localStorage.setItem('cart', JSON.stringify([
                { id: 1, name: 'Test Item', price: 5000, quantity: 2 }
            ]));
        });
    });

    it('should complete checkout process successfully', () => {
        // Fill out form
        cy.get('[name="name"]').type('<PERSON>');
        cy.get('[name="email"]').type('<EMAIL>');
        cy.get('[name="phone"]').type('1234567890');
        cy.get('[name="address"]').type('123 Test St');

        // Submit form
        cy.get('.checkout-form').submit();

        // Assert success
        cy.get('.success-message').should('be.visible');
        cy.url().should('include', '/confirmation.html');
    });

    it('should show validation errors for invalid inputs', () => {
        // Submit empty form
        cy.get('.checkout-form').submit();

        // Assert validation messages
        cy.get('.input-error').should('have.length.gt', 0);
    });
});