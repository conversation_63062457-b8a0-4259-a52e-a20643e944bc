# Developer Guide

## Development Environment Setup

1. **Prerequisites**
   - Node.js (v14+)
   - npm (v6+)
   - Git

2. **Local Setup**
   ```bash
   git clone https://github.com/your-org/magic-menu.git
   cd magic-menu
   npm install
   ```

## Code Organization

### Core Modules

- `assets/scripts/components.js`: Reusable UI components
- `assets/scripts/home.js`: Homepage-specific functionality
- `assets/scripts/menuDetail.js`: Menu item detail handling
- `assets/scripts/faq.js`: FAQ page functionality

### Key Classes

#### CartManager
Handles shopping cart operations:
```javascript
const cart = new CartManager();
cart.addItem(item);
cart.updateQuantity(itemId, quantity);
```

#### FormValidator
Validates form inputs:
```javascript
const validator = new FormValidator();
validator.validateEmail(email);
validator.validatePhone(phone);
```

## Best Practices

1. **Performance**
   - Use lazy loading for images
   - Implement debouncing for search inputs
   - Minimize DOM manipulations

2. **Code Style**
   - Use ES6+ features
   - Follow airbnb style guide
   - Use meaningful variable names

3. **Error Handling**
   ```javascript
   try {
     await api.processOrder(orderData);
   } catch (error) {
     ErrorHandler.log(error);
     UI.showErrorMessage(error.message);
   }
   ```

## Common Tasks

### Adding a New Page

1. Create HTML file in root directory
2. Add corresponding JS file in `assets/scripts/`
3. Update navigation links
4. Add tests in `tests/` directory

### Creating a New Component

1. Add component class to `components.js`
2. Create unit tests
3. Document usage examples
4. Update component library docs