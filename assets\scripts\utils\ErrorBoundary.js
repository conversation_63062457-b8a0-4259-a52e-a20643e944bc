export class ErrorBoundary {
    static logError(error, errorInfo) {
        // Log to your preferred error tracking service
        console.error('Error:', error);
        console.error('Error Info:', errorInfo);
        
        // You could send to a logging service here
        // Example: sendToLoggingService(error, errorInfo);
    }

    static handleError(error, componentName, fallbackUI) {
        this.logError(error, { componentName });
        
        if (fallbackUI) {
            return this.renderFallbackUI(fallbackUI, componentName);
        }
        
        return this.renderDefaultError(componentName);
    }

    static renderFallbackUI(fallbackUI, componentName) {
        const container = document.createElement('div');
        container.className = 'error-boundary-fallback';
        container.innerHTML = fallbackUI;
        return container;
    }

    static renderDefaultError(componentName) {
        const container = document.createElement('div');
        container.className = 'error-boundary-default';
        container.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-circle"></i>
                <h3>Something went wrong</h3>
                <p>There was an error loading this content. Please try refreshing the page.</p>
                <button onclick="window.location.reload()" class="retry-button">
                    <i class="fas fa-redo"></i> Retry
                </button>
            </div>
        `;
        return container;
    }
}