/**
 * Modern Cart Page Manager
 * Handles cart functionality with modern ES6+ features, better error handling,
 * and enhanced user experience
 */

import { CartManager } from './components.js';
import { CURRENCY_CONFIG, formatPrice } from './config/currency.js';
import Analytics from './services/Analytics.js';

class ModernCartManager {
    constructor() {
        this.cartManager = new CartManager();
        this.elements = {};
        this.state = {
            isLoading: false,
            items: [],
            totals: {
                subtotal: 0,
                tax: 0,
                deliveryFee: CURRENCY_CONFIG.deliveryFee,
                total: 0
            }
        };
        
        this.init();
    }

    /**
     * Initialize the cart manager
     */
    async init() {
        try {
            this.cacheElements();
            this.bindEvents();
            await this.loadCart();
            this.updateDisplay();
            this.initializeAccessibility();
        } catch (error) {
            console.error('Failed to initialize cart:', error);
            this.showError('Failed to load cart. Please refresh the page.');
        }
    }

    /**
     * Cache DOM elements for better performance
     */
    cacheElements() {
        this.elements = {
            // Main containers
            loadingState: document.querySelector('.loading-state'),
            emptyState: document.querySelector('.empty-cart-state'),
            itemsList: document.querySelector('.cart-items-list'),
            
            // Summary elements
            summarySubtotal: document.querySelector('.summary-subtotal'),
            summaryTax: document.querySelector('.summary-tax'),
            summaryDelivery: document.querySelector('.summary-delivery'),
            summaryTotal: document.querySelector('.summary-total'),
            
            // Action buttons
            checkoutBtn: document.querySelector('.checkout-btn'),
            clearCartBtn: document.querySelector('.clear-cart-btn'),
            
            // Promo code
            promoForm: document.querySelector('.promo-form'),
            promoInput: document.querySelector('.promo-input'),
            promoError: document.querySelector('#promo-error'),
            
            // Template
            itemTemplate: document.querySelector('#cart-item-template'),
            
            // Toast container
            toastContainer: document.querySelector('#toast-container'),
            
            // Cart count
            cartCount: document.querySelector('#cart-count')
        };
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Checkout button
        this.elements.checkoutBtn?.addEventListener('click', this.handleCheckout.bind(this));
        
        // Clear cart button
        this.elements.clearCartBtn?.addEventListener('click', this.handleClearCart.bind(this));
        
        // Promo code form
        this.elements.promoForm?.addEventListener('submit', this.handlePromoCode.bind(this));
        
        // Global cart updates
        window.addEventListener('cartUpdated', this.handleCartUpdate.bind(this));
        
        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));
        
        // Mobile menu toggle
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        mobileMenuToggle?.addEventListener('click', this.toggleMobileMenu.bind(this));
    }

    /**
     * Load cart data
     */
    async loadCart() {
        this.showLoading(true);
        
        try {
            // Try to load from API first, fallback to localStorage
            let items = [];
            
            try {
                const response = await fetch('/api/cart');
                if (response.ok) {
                    const data = await response.json();
                    items = data.items || [];
                }
            } catch (apiError) {
                console.warn('API unavailable, using localStorage:', apiError);
                items = this.cartManager.getCartItems() || [];
            }
            
            this.state.items = items;
            this.calculateTotals();
            
        } catch (error) {
            console.error('Failed to load cart:', error);
            this.showError('Failed to load cart items');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Calculate cart totals
     */
    calculateTotals() {
        const subtotal = this.state.items.reduce((sum, item) => {
            const price = parseFloat(item.price?.replace(/[^\d.-]/g, '') || 0);
            return sum + (price * (item.quantity || 1));
        }, 0);
        
        const tax = subtotal * CURRENCY_CONFIG.vatRate;
        const deliveryFee = subtotal > 0 ? CURRENCY_CONFIG.deliveryFee : 0;
        const total = subtotal + tax + deliveryFee;
        
        this.state.totals = {
            subtotal,
            tax,
            deliveryFee,
            total
        };
    }

    /**
     * Update the display
     */
    updateDisplay() {
        this.updateItemsList();
        this.updateSummary();
        this.updateCartCount();
        this.updateCheckoutButton();
        this.toggleEmptyState();
    }

    /**
     * Update items list
     */
    updateItemsList() {
        if (!this.elements.itemsList || !this.elements.itemTemplate) return;
        
        this.elements.itemsList.innerHTML = '';
        
        this.state.items.forEach((item, index) => {
            const itemElement = this.createItemElement(item, index);
            this.elements.itemsList.appendChild(itemElement);
        });
    }

    /**
     * Create item element from template
     */
    createItemElement(item, index) {
        const template = this.elements.itemTemplate.content.cloneNode(true);
        const itemElement = template.querySelector('.cart-item');
        
        // Set item data
        itemElement.dataset.itemId = item.id;
        itemElement.dataset.index = index;
        
        // Update content
        const image = template.querySelector('.item-image');
        const title = template.querySelector('.item-title');
        const description = template.querySelector('.item-description');
        const price = template.querySelector('.item-price');
        const totalPrice = template.querySelector('.item-total-price');
        const quantityInput = template.querySelector('.quantity-input');
        
        if (image) {
            image.src = item.image || 'assets/images/placeholder-food.jpg';
            image.alt = item.title || 'Food item';
        }
        
        if (title) title.textContent = item.title || 'Unknown Item';
        if (description) description.textContent = item.description || '';
        
        const itemPrice = parseFloat(item.price?.replace(/[^\d.-]/g, '') || 0);
        const itemTotal = itemPrice * (item.quantity || 1);
        
        if (price) price.textContent = formatPrice(itemPrice);
        if (totalPrice) totalPrice.textContent = formatPrice(itemTotal);
        if (quantityInput) quantityInput.value = item.quantity || 1;
        
        // Bind item events
        this.bindItemEvents(template, item);
        
        return template;
    }

    /**
     * Bind events for individual cart items
     */
    bindItemEvents(template, item) {
        const removeBtn = template.querySelector('.remove-item-btn');
        const decreaseBtn = template.querySelector('.quantity-btn.decrease');
        const increaseBtn = template.querySelector('.quantity-btn.increase');
        const quantityInput = template.querySelector('.quantity-input');
        const saveForLaterBtn = template.querySelector('.save-for-later-btn');
        
        // Remove item
        removeBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.removeItem(item.id);
        });
        
        // Quantity controls
        decreaseBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.updateQuantity(item.id, Math.max(1, (item.quantity || 1) - 1));
        });
        
        increaseBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.updateQuantity(item.id, Math.min(99, (item.quantity || 1) + 1));
        });
        
        // Quantity input
        quantityInput?.addEventListener('change', (e) => {
            const newQuantity = Math.max(1, Math.min(99, parseInt(e.target.value) || 1));
            this.updateQuantity(item.id, newQuantity);
        });
        
        // Save for later
        saveForLaterBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.saveForLater(item.id);
        });
    }

    /**
     * Update item quantity
     */
    async updateQuantity(itemId, newQuantity) {
        try {
            const itemIndex = this.state.items.findIndex(item => item.id === itemId);
            if (itemIndex === -1) return;
            
            this.state.items[itemIndex].quantity = newQuantity;
            
            // Update in storage/API
            await this.saveCart();
            
            this.calculateTotals();
            this.updateDisplay();
            
            // Analytics
            if (Analytics.consent) {
                Analytics.track('update_cart_quantity', {
                    item_id: itemId,
                    quantity: newQuantity
                });
            }
            
            this.showToast('Quantity updated', 'success');
            
        } catch (error) {
            console.error('Failed to update quantity:', error);
            this.showError('Failed to update quantity');
        }
    }

    /**
     * Remove item from cart
     */
    async removeItem(itemId) {
        try {
            const itemIndex = this.state.items.findIndex(item => item.id === itemId);
            if (itemIndex === -1) return;
            
            const removedItem = this.state.items[itemIndex];
            this.state.items.splice(itemIndex, 1);
            
            // Update in storage/API
            await this.saveCart();
            
            this.calculateTotals();
            this.updateDisplay();
            
            // Analytics
            if (Analytics.consent) {
                Analytics.track('remove_from_cart', {
                    item_id: itemId,
                    item_name: removedItem.title
                });
            }
            
            this.showToast(`${removedItem.title} removed from cart`, 'success');
            
        } catch (error) {
            console.error('Failed to remove item:', error);
            this.showError('Failed to remove item');
        }
    }

    /**
     * Save for later functionality
     */
    async saveForLater(itemId) {
        try {
            // Implementation would depend on backend support
            this.showToast('Save for later feature coming soon!', 'warning');
        } catch (error) {
            console.error('Failed to save for later:', error);
            this.showError('Failed to save item for later');
        }
    }

    /**
     * Handle clear cart
     */
    async handleClearCart() {
        if (!confirm('Are you sure you want to clear your cart?')) return;
        
        try {
            this.state.items = [];
            await this.saveCart();
            
            this.calculateTotals();
            this.updateDisplay();
            
            // Analytics
            if (Analytics.consent) {
                Analytics.track('clear_cart');
            }
            
            this.showToast('Cart cleared', 'success');
            
        } catch (error) {
            console.error('Failed to clear cart:', error);
            this.showError('Failed to clear cart');
        }
    }

    /**
     * Handle checkout
     */
    async handleCheckout() {
        if (this.state.items.length === 0) {
            this.showError('Your cart is empty');
            return;
        }
        
        if (this.state.totals.subtotal < CURRENCY_CONFIG.minimumOrder) {
            this.showError(`Minimum order amount is ${formatPrice(CURRENCY_CONFIG.minimumOrder)}`);
            return;
        }
        
        try {
            // Show loading state
            this.elements.checkoutBtn.disabled = true;
            this.elements.checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            
            // Analytics
            if (Analytics.consent) {
                Analytics.trackEcommerce('begin_checkout', {
                    items: this.state.items,
                    value: this.state.totals.total,
                    shipping: this.state.totals.deliveryFee,
                    tax: this.state.totals.tax
                });
            }
            
            // Navigate to checkout
            setTimeout(() => {
                window.location.href = 'modern-checkout.html';
            }, 500);
            
        } catch (error) {
            console.error('Checkout failed:', error);
            this.showError('Failed to proceed to checkout');
            
            // Reset button
            this.elements.checkoutBtn.disabled = false;
            this.elements.checkoutBtn.innerHTML = '<i class="fas fa-lock"></i> Proceed to Checkout';
        }
    }

    /**
     * Handle promo code
     */
    async handlePromoCode(e) {
        e.preventDefault();
        
        const promoCode = this.elements.promoInput?.value.trim();
        if (!promoCode) return;
        
        try {
            // Mock promo code validation
            const validCodes = ['SAVE10', 'WELCOME20', 'STUDENT15'];
            
            if (validCodes.includes(promoCode.toUpperCase())) {
                this.showToast('Promo code applied successfully!', 'success');
                this.elements.promoError.textContent = '';
            } else {
                throw new Error('Invalid promo code');
            }
            
        } catch (error) {
            this.elements.promoError.textContent = error.message;
            this.elements.promoError.classList.add('show');
        }
    }

    /**
     * Save cart to storage/API
     */
    async saveCart() {
        try {
            // Try API first
            try {
                await fetch('/api/cart', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ items: this.state.items })
                });
            } catch (apiError) {
                // Fallback to localStorage
                localStorage.setItem('cartItems', JSON.stringify(this.state.items));
            }
        } catch (error) {
            console.error('Failed to save cart:', error);
            throw error;
        }
    }

    /**
     * Update summary display
     */
    updateSummary() {
        if (this.elements.summarySubtotal) {
            this.elements.summarySubtotal.textContent = formatPrice(this.state.totals.subtotal);
        }
        if (this.elements.summaryTax) {
            this.elements.summaryTax.textContent = formatPrice(this.state.totals.tax);
        }
        if (this.elements.summaryDelivery) {
            this.elements.summaryDelivery.textContent = formatPrice(this.state.totals.deliveryFee);
        }
        if (this.elements.summaryTotal) {
            this.elements.summaryTotal.textContent = formatPrice(this.state.totals.total);
        }
    }

    /**
     * Update cart count
     */
    updateCartCount() {
        const totalItems = this.state.items.reduce((sum, item) => sum + (item.quantity || 1), 0);

        if (this.elements.cartCount) {
            this.elements.cartCount.textContent = totalItems;
        }

        // Update all cart count elements
        document.querySelectorAll('.cart-count').forEach(element => {
            element.textContent = totalItems;
        });

        // Dispatch cart updated event for header integration
        window.dispatchEvent(new CustomEvent('cartUpdated', {
            detail: { count: totalItems, items: this.state.items }
        }));
    }

    /**
     * Update checkout button state
     */
    updateCheckoutButton() {
        if (!this.elements.checkoutBtn) return;
        
        const hasItems = this.state.items.length > 0;
        const meetsMinimum = this.state.totals.subtotal >= CURRENCY_CONFIG.minimumOrder;
        
        this.elements.checkoutBtn.disabled = !hasItems || !meetsMinimum;
        
        if (!hasItems) {
            this.elements.checkoutBtn.innerHTML = '<i class="fas fa-shopping-cart"></i> Cart is Empty';
        } else if (!meetsMinimum) {
            this.elements.checkoutBtn.innerHTML = `<i class="fas fa-exclamation-triangle"></i> Minimum ${formatPrice(CURRENCY_CONFIG.minimumOrder)}`;
        } else {
            this.elements.checkoutBtn.innerHTML = '<i class="fas fa-lock"></i> Proceed to Checkout';
        }
    }

    /**
     * Toggle empty state
     */
    toggleEmptyState() {
        const hasItems = this.state.items.length > 0;
        
        if (this.elements.emptyState) {
            this.elements.emptyState.style.display = hasItems ? 'none' : 'block';
        }
        if (this.elements.itemsList) {
            this.elements.itemsList.style.display = hasItems ? 'block' : 'none';
        }
        if (this.elements.clearCartBtn) {
            this.elements.clearCartBtn.style.display = hasItems ? 'inline-flex' : 'none';
        }
    }

    /**
     * Show/hide loading state
     */
    showLoading(show) {
        this.state.isLoading = show;
        
        if (this.elements.loadingState) {
            this.elements.loadingState.style.display = show ? 'flex' : 'none';
        }
        if (this.elements.itemsList) {
            this.elements.itemsList.style.display = show ? 'none' : 'block';
        }
        if (this.elements.emptyState) {
            this.elements.emptyState.style.display = show ? 'none' : 'block';
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        if (!this.elements.toastContainer) return;
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="fas fa-${this.getToastIcon(type)}"></i>
            <span>${message}</span>
            <button class="toast-close" aria-label="Close notification">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Close button
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.remove();
        });
        
        this.elements.toastContainer.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * Get toast icon based on type
     */
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * Handle cart updates from other parts of the app
     */
    handleCartUpdate(event) {
        this.loadCart();
    }

    /**
     * Handle keyboard navigation
     */
    handleKeyboardNavigation(e) {
        // Delete key to remove focused item
        if (e.key === 'Delete' && document.activeElement.classList.contains('cart-item')) {
            const itemId = document.activeElement.dataset.itemId;
            if (itemId) {
                this.removeItem(itemId);
            }
        }
        
        // Escape key to close mobile menu
        if (e.key === 'Escape') {
            const mobileMenu = document.querySelector('.nav-list');
            if (mobileMenu && mobileMenu.classList.contains('active')) {
                this.toggleMobileMenu();
            }
        }
    }

    /**
     * Toggle mobile menu
     */
    toggleMobileMenu() {
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navList = document.querySelector('.nav-list');
        
        if (mobileMenuToggle && navList) {
            const isExpanded = mobileMenuToggle.getAttribute('aria-expanded') === 'true';
            
            mobileMenuToggle.setAttribute('aria-expanded', !isExpanded);
            navList.classList.toggle('active');
            document.body.classList.toggle('menu-open');
        }
    }

    /**
     * Initialize accessibility features
     */
    initializeAccessibility() {
        // Add ARIA labels and descriptions
        const cartItems = document.querySelectorAll('.cart-item');
        cartItems.forEach((item, index) => {
            item.setAttribute('aria-label', `Cart item ${index + 1}`);
        });
        
        // Announce cart updates to screen readers
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        document.body.appendChild(announcer);
        
        this.announcer = announcer;
    }

    /**
     * Announce to screen readers
     */
    announce(message) {
        if (this.announcer) {
            this.announcer.textContent = message;
            setTimeout(() => {
                this.announcer.textContent = '';
            }, 1000);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ModernCartManager();
});

// Export for testing
export default ModernCartManager;
