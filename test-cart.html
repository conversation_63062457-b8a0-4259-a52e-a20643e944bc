<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #ff7a00;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #e66d00;
        }
        .cart-items {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Cart Functionality Test</h1>
    
    <div class="test-section">
        <h2>Add Test Items to Cart</h2>
        <button onclick="addTestItem1()">Add Jollof Rice (₦2,500)</button>
        <button onclick="addTestItem2()">Add Egusi Soup (₦3,000)</button>
        <button onclick="addTestItem3()">Add Amala & Ewedu (₦2,000)</button>
    </div>
    
    <div class="test-section">
        <h2>Cart Actions</h2>
        <button onclick="viewCart()">View Cart Items</button>
        <button onclick="clearCart()">Clear Cart</button>
        <button onclick="openCartPage()">Open Cart Page</button>
    </div>
    
    <div class="test-section">
        <h2>Current Cart Contents</h2>
        <div id="cart-display" class="cart-items">
            <p>No items in cart</p>
        </div>
    </div>

    <script>
        // Test data
        const testItems = [
            {
                id: 'jollof-rice-001',
                title: 'Jollof Rice',
                description: 'Delicious Nigerian jollof rice with vegetables',
                price: '₦2,500',
                image: 'assets/images/jollof-rice.jpg',
                quantity: 1
            },
            {
                id: 'egusi-soup-002',
                title: 'Egusi Soup',
                description: 'Traditional Nigerian egusi soup with meat',
                price: '₦3,000',
                image: 'assets/images/egusi.jpg',
                quantity: 1
            },
            {
                id: 'amala-003',
                title: 'Amala & Ewedu',
                description: 'Amala served with ewedu and stew',
                price: '₦2,000',
                image: 'assets/images/amala.jpg',
                quantity: 1
            }
        ];

        function addTestItem1() {
            addItemToCart(testItems[0]);
        }

        function addTestItem2() {
            addItemToCart(testItems[1]);
        }

        function addTestItem3() {
            addItemToCart(testItems[2]);
        }

        function addItemToCart(item) {
            try {
                let cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
                
                // Check if item already exists
                const existingIndex = cartItems.findIndex(cartItem => cartItem.id === item.id);
                
                if (existingIndex > -1) {
                    cartItems[existingIndex].quantity += 1;
                } else {
                    cartItems.push({...item});
                }
                
                localStorage.setItem('cartItems', JSON.stringify(cartItems));
                updateCartDisplay();
                alert(`${item.title} added to cart!`);
            } catch (error) {
                console.error('Error adding item to cart:', error);
                alert('Failed to add item to cart');
            }
        }

        function viewCart() {
            const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
            console.log('Cart Items:', cartItems);
            updateCartDisplay();
        }

        function clearCart() {
            localStorage.removeItem('cartItems');
            updateCartDisplay();
            alert('Cart cleared!');
        }

        function openCartPage() {
            window.open('cart.html', '_blank');
        }

        function updateCartDisplay() {
            const cartDisplay = document.getElementById('cart-display');
            const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
            
            if (cartItems.length === 0) {
                cartDisplay.innerHTML = '<p>No items in cart</p>';
                return;
            }
            
            let html = '<h3>Cart Items:</h3>';
            let total = 0;
            
            cartItems.forEach(item => {
                const price = parseFloat(item.price.replace(/[^\d.-]/g, ''));
                const itemTotal = price * item.quantity;
                total += itemTotal;
                
                html += `
                    <div style="border-bottom: 1px solid #ddd; padding: 10px 0;">
                        <strong>${item.title}</strong><br>
                        Price: ${item.price}<br>
                        Quantity: ${item.quantity}<br>
                        Subtotal: ₦${itemTotal.toFixed(2)}
                    </div>
                `;
            });
            
            html += `<div style="margin-top: 15px; font-weight: bold;">Total: ₦${total.toFixed(2)}</div>`;
            cartDisplay.innerHTML = html;
        }

        // Initialize display
        updateCartDisplay();
    </script>
</body>
</html>
