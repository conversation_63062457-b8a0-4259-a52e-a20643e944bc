import { <PERSON><PERSON>, CartManager } from './components.js';

export default class MenuDetail {
    constructor() {
        console.log('MenuDetail initialized'); // Debug log
        this.currentItem = null;
        this.cartManager = new CartManager();
        this.initializeModal();
    }

    initializeModal() {
        console.log('Initializing modal'); // Debug log

        // The modal already exists in the HTML, so we don't need to create it
        // Just verify it exists
        if (!document.getElementById('itemDetailModal')) {
            console.error('Modal element not found in HTML');
            return;
        }

        // Initialize modal elements
        this.modal = document.getElementById('itemDetailModal');
        this.modalImage = document.getElementById('modalImage');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalDescription = document.getElementById('modalDescription');
        this.modalPrice = document.getElementById('modalPrice');
        this.modalCalories = document.getElementById('modalCalories');
        this.modalProtein = document.getElementById('modalProtein');
        this.modalCarbs = document.getElementById('modalCarbs');
        this.modalIngredients = document.getElementById('modalIngredients');
        this.modalQuantity = document.getElementById('modalQuantity');
        this.modalAddToCart = document.getElementById('modalAddToCart');

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Close modal when clicking the X or outside the modal
        const closeBtn = this.modal.querySelector('.close-modal');
        closeBtn.addEventListener('click', () => this.closeModal());
        
        window.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });

        // Quantity controls
        const decreaseBtn = this.modal.querySelector('.quantity-btn.decrease');
        const increaseBtn = this.modal.querySelector('.quantity-btn.increase');
        
        decreaseBtn.addEventListener('click', () => {
            const currentVal = parseInt(this.modalQuantity.value);
            if (currentVal > 1) this.modalQuantity.value = currentVal - 1;
        });

        increaseBtn.addEventListener('click', () => {
            const currentVal = parseInt(this.modalQuantity.value);
            if (currentVal < 10) this.modalQuantity.value = currentVal + 1;
        });

        // Add to cart
        this.modalAddToCart.addEventListener('click', () => this.addToCart());
    }

    openModal(itemData) {
        console.log('Opening modal with data:', itemData); // Debug log
        this.currentItem = itemData;

        // Update modal content
        this.modalImage.src = itemData.image;
        this.modalImage.alt = itemData.title;
        this.modalTitle.textContent = itemData.title;
        this.modalDescription.textContent = itemData.description;
        this.modalPrice.textContent = itemData.price;
        this.modalCalories.textContent = itemData.nutrition.calories;
        this.modalProtein.textContent = itemData.nutrition.protein;
        this.modalCarbs.textContent = itemData.nutrition.carbs;
        this.modalIngredients.textContent = itemData.ingredients.join(', ');

        // Reset quantity
        this.modalQuantity.value = 1;

        // Show modal
        this.modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent scrolling
    }

    closeModal() {
        this.modal.style.display = 'none';
        document.body.style.overflow = ''; // Restore scrolling
    }

    async addToCart() {
        try {
            if (!this.currentItem) {
                throw new Error('No item selected');
            }

            const quantity = parseInt(this.modalQuantity.value) || 1;
            const item = {
                id: this.currentItem.id,
                title: this.currentItem.title,
                description: this.currentItem.description,
                price: this.currentItem.price,
                image: this.currentItem.image,
                quantity: quantity
            };

            // Use CartManager to add item
            await this.cartManager.addToCart(item);

            // Update cart icon
            const cartItems = CartManager.getCartItems();
            const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = totalItems;
            });

            // Dispatch custom event for analytics
            document.dispatchEvent(new CustomEvent('cartUpdated', {
                detail: {
                    action: 'add',
                    item: item
                }
            }));

            // Close modal
            this.closeModal();

        } catch (error) {
            console.error('Add to cart error:', error);
            Alert.show('Failed to add item to cart', 'error');
        }
    }
}
