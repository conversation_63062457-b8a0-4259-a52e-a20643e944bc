<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Pages Integration Test</title>
    
    <!-- Modern CSS -->
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/modern-base.css">
    <link rel="stylesheet" href="assets/css/modern-header.css">
    <link rel="stylesheet" href="assets/css/modern-cart.css">
    <link rel="stylesheet" href="assets/css/modern-checkout.css">
    <link rel="stylesheet" href="assets/css/modern-confirmation.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--white);
        }
        
        .test-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--background-color);
        }
        
        .test-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            color: white;
        }
        
        .test-status.pending {
            background: var(--warning-color);
        }
        
        .test-status.success {
            background: var(--success-color);
        }
        
        .test-status.error {
            background: var(--error-color);
        }
        
        .test-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: var(--radius-md);
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
        
        .test-result.success {
            background: var(--success-light);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }
        
        .test-result.error {
            background: var(--error-light);
            color: var(--error-color);
            border: 1px solid var(--error-color);
        }
        
        .page-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .page-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--radius-lg);
            transition: var(--transition-default);
        }
        
        .page-link:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .page-link i {
            font-size: 1.5rem;
        }
        
        .logs {
            max-height: 300px;
            overflow-y: auto;
            background: #1a1a1a;
            color: #00ff00;
            padding: 1rem;
            border-radius: var(--radius-md);
            font-family: monospace;
            font-size: 0.8rem;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
        }
        
        .log-timestamp {
            color: #888;
        }
        
        .log-level-info { color: #00ff00; }
        .log-level-warn { color: #ffff00; }
        .log-level-error { color: #ff0000; }
    </style>
</head>
<body>
    <div class="test-container">
        <header class="test-header">
            <h1>Modern Pages Integration Test</h1>
            <p>Test the new cart, checkout, and confirmation pages</p>
        </header>
        
        <!-- Page Links -->
        <section class="page-links">
            <a href="modern-cart.html" class="page-link">
                <i class="fas fa-shopping-cart"></i>
                <div>
                    <h3>Modern Cart</h3>
                    <p>Enhanced cart experience</p>
                </div>
            </a>
            
            <a href="modern-checkout.html" class="page-link">
                <i class="fas fa-credit-card"></i>
                <div>
                    <h3>Modern Checkout</h3>
                    <p>Streamlined checkout process</p>
                </div>
            </a>
            
            <a href="modern-confirmation.html?orderId=TEST-123" class="page-link">
                <i class="fas fa-check-circle"></i>
                <div>
                    <h3>Modern Confirmation</h3>
                    <p>Order confirmation page</p>
                </div>
            </a>
        </section>
        
        <!-- Currency System Test -->
        <section class="test-section">
            <div class="test-header">
                <div class="test-status pending" id="currency-status">?</div>
                <h2>Currency System Test</h2>
            </div>
            
            <div class="test-actions">
                <button class="btn btn-primary" onclick="testCurrency()">
                    <i class="fas fa-dollar-sign"></i>
                    Test Currency Functions
                </button>
            </div>
            
            <div id="currency-result" class="test-result" style="display: none;"></div>
        </section>
        
        <!-- API Service Test -->
        <section class="test-section">
            <div class="test-header">
                <div class="test-status pending" id="api-status">?</div>
                <h2>API Service Test</h2>
            </div>
            
            <div class="test-actions">
                <button class="btn btn-primary" onclick="testApiService()">
                    <i class="fas fa-server"></i>
                    Test API Service
                </button>
                <button class="btn btn-secondary" onclick="testApiCache()">
                    <i class="fas fa-database"></i>
                    Test API Cache
                </button>
            </div>
            
            <div id="api-result" class="test-result" style="display: none;"></div>
        </section>
        
        <!-- Error Handler Test -->
        <section class="test-section">
            <div class="test-header">
                <div class="test-status pending" id="error-status">?</div>
                <h2>Error Handler Test</h2>
            </div>
            
            <div class="test-actions">
                <button class="btn btn-primary" onclick="testErrorHandler()">
                    <i class="fas fa-exclamation-triangle"></i>
                    Test Error Handling
                </button>
                <button class="btn btn-warning" onclick="triggerTestError()">
                    <i class="fas fa-bug"></i>
                    Trigger Test Error
                </button>
            </div>
            
            <div id="error-result" class="test-result" style="display: none;"></div>
        </section>
        
        <!-- Utilities Test -->
        <section class="test-section">
            <div class="test-header">
                <div class="test-status pending" id="utils-status">?</div>
                <h2>Modern Utils Test</h2>
            </div>
            
            <div class="test-actions">
                <button class="btn btn-primary" onclick="testUtils()">
                    <i class="fas fa-tools"></i>
                    Test Utilities
                </button>
                <button class="btn btn-secondary" onclick="testValidation()">
                    <i class="fas fa-check-double"></i>
                    Test Validation
                </button>
            </div>
            
            <div id="utils-result" class="test-result" style="display: none;"></div>
        </section>
        
        <!-- Cart Integration Test -->
        <section class="test-section">
            <div class="test-header">
                <div class="test-status pending" id="cart-status">?</div>
                <h2>Cart Integration Test</h2>
            </div>
            
            <div class="test-actions">
                <button class="btn btn-primary" onclick="testCartIntegration()">
                    <i class="fas fa-shopping-basket"></i>
                    Test Cart Functions
                </button>
                <button class="btn btn-secondary" onclick="populateTestCart()">
                    <i class="fas fa-plus"></i>
                    Add Test Items
                </button>
                <button class="btn btn-outline" onclick="clearTestCart()">
                    <i class="fas fa-trash"></i>
                    Clear Cart
                </button>
            </div>
            
            <div id="cart-result" class="test-result" style="display: none;"></div>
        </section>
        
        <!-- Performance Test -->
        <section class="test-section">
            <div class="test-header">
                <div class="test-status pending" id="performance-status">?</div>
                <h2>Performance Test</h2>
            </div>
            
            <div class="test-actions">
                <button class="btn btn-primary" onclick="testPerformance()">
                    <i class="fas fa-tachometer-alt"></i>
                    Run Performance Tests
                </button>
            </div>
            
            <div id="performance-result" class="test-result" style="display: none;"></div>
        </section>
        
        <!-- System Logs -->
        <section class="test-section">
            <div class="test-header">
                <div class="test-status success" id="logs-status">✓</div>
                <h2>System Logs</h2>
            </div>
            
            <div class="test-actions">
                <button class="btn btn-secondary" onclick="clearLogs()">
                    <i class="fas fa-broom"></i>
                    Clear Logs
                </button>
                <button class="btn btn-outline" onclick="exportLogs()">
                    <i class="fas fa-download"></i>
                    Export Logs
                </button>
            </div>
            
            <div id="system-logs" class="logs"></div>
        </section>
    </div>
    
    <!-- Toast Container -->
    <div id="toast-container" class="toast-container" aria-live="polite" aria-atomic="true"></div>
    
    <!-- Scripts -->
    <script type="module">
        import { formatPrice, parsePrice, calculateTax, calculateTotal, CURRENCY_CONFIG } from './assets/scripts/config/currency.js';
        import { apiService } from './assets/scripts/services/ModernApiService.js';
        import { errorHandler } from './assets/scripts/utils/ModernErrorHandler.js';
        import ModernUtils from './assets/scripts/utils/ModernUtils.js';
        
        // Make functions globally available for testing
        window.testFunctions = {
            formatPrice,
            parsePrice,
            calculateTax,
            calculateTotal,
            CURRENCY_CONFIG,
            apiService,
            errorHandler,
            ModernUtils
        };
        
        // Logging system
        window.systemLogs = [];
        
        window.log = function(level, message, data = null) {
            const timestamp = new Date().toISOString();
            const logEntry = {
                timestamp,
                level,
                message,
                data
            };
            
            window.systemLogs.push(logEntry);
            
            // Update logs display
            const logsContainer = document.getElementById('system-logs');
            if (logsContainer) {
                const logElement = document.createElement('div');
                logElement.className = 'log-entry';
                logElement.innerHTML = `
                    <span class="log-timestamp">[${timestamp}]</span>
                    <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                    ${message}
                    ${data ? `\n${JSON.stringify(data, null, 2)}` : ''}
                `;
                logsContainer.appendChild(logElement);
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }
            
            // Also log to console
            console[level](message, data);
        };
        
        // Initialize logging
        window.log('info', 'Integration test page loaded');
        
        // Test status helpers
        window.setTestStatus = function(testId, status, message = '') {
            const statusElement = document.getElementById(`${testId}-status`);
            const resultElement = document.getElementById(`${testId}-result`);
            
            if (statusElement) {
                statusElement.className = `test-status ${status}`;
                statusElement.textContent = status === 'success' ? '✓' : status === 'error' ? '✗' : '?';
            }
            
            if (resultElement && message) {
                resultElement.style.display = 'block';
                resultElement.className = `test-result ${status}`;
                resultElement.textContent = message;
            }
        };
        
        // Show toast notification
        window.showToast = function(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            if (!toastContainer) return;
            
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button class="toast-close" aria-label="Close notification">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            toast.querySelector('.toast-close').addEventListener('click', () => {
                toast.remove();
            });
            
            toastContainer.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 5000);
        };
    </script>
    
    <script src="test-functions.js"></script>
</body>
</html>
