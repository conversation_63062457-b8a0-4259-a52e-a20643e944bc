/**
 * Modern Checkout Page Manager
 * Handles checkout form validation, payment processing, and order submission
 * with modern ES6+ features and enhanced user experience
 */

import { CartManager } from './components.js';
import { CURRENCY_CONFIG, formatPrice } from './config/currency.js';
import Analytics from './services/Analytics.js';

class ModernCheckoutManager {
    constructor() {
        this.cartManager = new CartManager();
        this.elements = {};
        this.state = {
            isLoading: false,
            cartItems: [],
            totals: {
                subtotal: 0,
                tax: 0,
                deliveryFee: CURRENCY_CONFIG.deliveryFee,
                total: 0
            },
            formData: {},
            validationErrors: {}
        };
        
        this.validators = {
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            phone: /^(\+234|0)[789][01]\d{8}$/,
            required: /\S+/
        };
        
        this.init();
    }

    /**
     * Initialize the checkout manager
     */
    async init() {
        try {
            this.cacheElements();
            this.bindEvents();
            await this.loadCartData();
            this.updateOrderSummary();
            this.initializeFormValidation();
            this.initializeAccessibility();
            this.trackCheckoutStart();
        } catch (error) {
            console.error('Failed to initialize checkout:', error);
            this.showError('Failed to load checkout. Please refresh the page.');
        }
    }

    /**
     * Cache DOM elements for better performance
     */
    cacheElements() {
        this.elements = {
            // Form elements
            form: document.querySelector('#checkout-form'),
            submitBtn: document.querySelector('.submit-btn'),
            
            // Delivery method
            deliveryOptions: document.querySelectorAll('input[name="deliveryMethod"]'),
            deliveryAddressSection: document.querySelector('.delivery-address-section'),
            
            // Customer info
            customerName: document.querySelector('#customer-name'),
            customerEmail: document.querySelector('#customer-email'),
            customerPhone: document.querySelector('#customer-phone'),
            
            // Address fields
            deliveryAddress: document.querySelector('#delivery-address'),
            deliveryCity: document.querySelector('#delivery-city'),
            deliveryState: document.querySelector('#delivery-state'),
            deliveryNotes: document.querySelector('#delivery-notes'),
            
            // Payment method
            paymentOptions: document.querySelectorAll('input[name="paymentMethod"]'),
            
            // Order notes
            orderNotes: document.querySelector('#order-notes'),
            
            // Terms and conditions
            termsAgreement: document.querySelector('#terms-agreement'),
            marketingConsent: document.querySelector('#marketing-consent'),
            
            // Order summary
            itemsList: document.querySelector('.items-list'),
            orderSubtotal: document.querySelector('.order-subtotal'),
            orderTax: document.querySelector('.order-tax'),
            orderDelivery: document.querySelector('.order-delivery'),
            orderTotal: document.querySelector('.order-total'),
            deliveryFeeLine: document.querySelector('.delivery-fee-line'),
            deliveryTime: document.querySelector('.delivery-time'),
            
            // Toast container
            toastContainer: document.querySelector('#toast-container'),
            
            // Cart count
            cartCount: document.querySelector('#cart-count')
        };
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Form submission
        this.elements.form?.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Delivery method changes
        this.elements.deliveryOptions.forEach(option => {
            option.addEventListener('change', this.handleDeliveryMethodChange.bind(this));
        });
        
        // Payment method changes
        this.elements.paymentOptions.forEach(option => {
            option.addEventListener('change', this.handlePaymentMethodChange.bind(this));
        });
        
        // Real-time form validation
        const formInputs = this.elements.form?.querySelectorAll('input, select, textarea');
        formInputs?.forEach(input => {
            input.addEventListener('blur', this.validateField.bind(this));
            input.addEventListener('input', this.clearFieldError.bind(this));
        });
        
        // Terms agreement
        this.elements.termsAgreement?.addEventListener('change', this.updateSubmitButton.bind(this));
        
        // Mobile menu toggle
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        mobileMenuToggle?.addEventListener('click', this.toggleMobileMenu.bind(this));
        
        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));
    }

    /**
     * Load cart data
     */
    async loadCartData() {
        try {
            // Try to load from API first, fallback to localStorage
            let items = [];
            
            try {
                const response = await fetch('/api/cart');
                if (response.ok) {
                    const data = await response.json();
                    items = data.items || [];
                }
            } catch (apiError) {
                console.warn('API unavailable, using localStorage:', apiError);
                items = this.cartManager.getCartItems() || [];
            }
            
            if (items.length === 0) {
                // Redirect to cart if empty
                this.showError('Your cart is empty. Redirecting to menu...');
                setTimeout(() => {
                    window.location.href = 'menu.html';
                }, 2000);
                return;
            }
            
            this.state.cartItems = items;
            this.calculateTotals();
            
        } catch (error) {
            console.error('Failed to load cart data:', error);
            this.showError('Failed to load cart items');
        }
    }

    /**
     * Calculate order totals
     */
    calculateTotals() {
        const subtotal = this.state.cartItems.reduce((sum, item) => {
            const price = parseFloat(item.price?.replace(/[^\d.-]/g, '') || 0);
            return sum + (price * (item.quantity || 1));
        }, 0);
        
        const tax = subtotal * CURRENCY_CONFIG.vatRate;
        
        // Check if delivery is selected
        const isDelivery = document.querySelector('input[name="deliveryMethod"]:checked')?.value === 'delivery';
        const deliveryFee = isDelivery ? CURRENCY_CONFIG.deliveryFee : 0;
        
        const total = subtotal + tax + deliveryFee;
        
        this.state.totals = {
            subtotal,
            tax,
            deliveryFee,
            total
        };
    }

    /**
     * Update order summary display
     */
    updateOrderSummary() {
        // Update items list
        if (this.elements.itemsList) {
            this.elements.itemsList.innerHTML = '';
            
            this.state.cartItems.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'order-item';
                
                const price = parseFloat(item.price?.replace(/[^\d.-]/g, '') || 0);
                const totalPrice = price * (item.quantity || 1);
                
                itemElement.innerHTML = `
                    <span class="item-name">${item.title || 'Unknown Item'}</span>
                    <span class="item-quantity">${item.quantity || 1}</span>
                    <span class="item-price">${formatPrice(totalPrice)}</span>
                `;
                
                this.elements.itemsList.appendChild(itemElement);
            });
        }
        
        // Update totals
        if (this.elements.orderSubtotal) {
            this.elements.orderSubtotal.textContent = formatPrice(this.state.totals.subtotal);
        }
        if (this.elements.orderTax) {
            this.elements.orderTax.textContent = formatPrice(this.state.totals.tax);
        }
        if (this.elements.orderDelivery) {
            this.elements.orderDelivery.textContent = formatPrice(this.state.totals.deliveryFee);
        }
        if (this.elements.orderTotal) {
            this.elements.orderTotal.textContent = formatPrice(this.state.totals.total);
        }
        
        // Update cart count
        const totalItems = this.state.cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
        if (this.elements.cartCount) {
            this.elements.cartCount.textContent = totalItems;
        }
    }

    /**
     * Handle delivery method change
     */
    handleDeliveryMethodChange(e) {
        const isDelivery = e.target.value === 'delivery';
        
        // Show/hide delivery address section
        if (this.elements.deliveryAddressSection) {
            this.elements.deliveryAddressSection.classList.toggle('hidden', !isDelivery);
            
            // Update required fields
            const addressFields = this.elements.deliveryAddressSection.querySelectorAll('input[required], select[required]');
            addressFields.forEach(field => {
                field.required = isDelivery;
            });
        }
        
        // Update delivery fee
        if (this.elements.deliveryFeeLine) {
            this.elements.deliveryFeeLine.classList.toggle('hidden', !isDelivery);
        }
        
        // Update delivery time
        if (this.elements.deliveryTime) {
            this.elements.deliveryTime.textContent = isDelivery ? '30-45 minutes' : '15-20 minutes';
        }
        
        // Recalculate totals
        this.calculateTotals();
        this.updateOrderSummary();
        
        // Analytics
        if (Analytics.consent) {
            Analytics.track('delivery_method_selected', {
                method: e.target.value
            });
        }
    }

    /**
     * Handle payment method change
     */
    handlePaymentMethodChange(e) {
        // Analytics
        if (Analytics.consent) {
            Analytics.track('payment_method_selected', {
                method: e.target.value
            });
        }
        
        // Update submit button text based on payment method
        const btnText = this.elements.submitBtn?.querySelector('.btn-text');
        if (btnText) {
            switch (e.target.value) {
                case 'card':
                    btnText.textContent = 'Pay Now';
                    break;
                case 'transfer':
                    btnText.textContent = 'Generate Transfer Details';
                    break;
                case 'cash':
                    btnText.textContent = 'Place Order';
                    break;
                default:
                    btnText.textContent = 'Complete Order';
            }
        }
    }

    /**
     * Initialize form validation
     */
    initializeFormValidation() {
        // Set up initial validation state
        this.updateSubmitButton();
        
        // Add validation indicators
        const requiredFields = this.elements.form?.querySelectorAll('[required]');
        requiredFields?.forEach(field => {
            const label = document.querySelector(`label[for="${field.id}"]`);
            if (label && !label.textContent.includes('*')) {
                label.innerHTML = label.innerHTML.replace(/(\w+)/, '$1 *');
            }
        });
    }

    /**
     * Validate individual field
     */
    validateField(e) {
        const field = e.target;
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';
        
        // Required field validation
        if (field.required && !this.validators.required.test(value)) {
            isValid = false;
            errorMessage = 'This field is required';
        }
        
        // Specific field validations
        if (value && isValid) {
            switch (fieldName) {
                case 'customerEmail':
                    if (!this.validators.email.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                    break;
                    
                case 'customerPhone':
                    if (!this.validators.phone.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid Nigerian phone number';
                    }
                    break;
                    
                case 'customerName':
                    if (value.length < 2) {
                        isValid = false;
                        errorMessage = 'Name must be at least 2 characters long';
                    }
                    break;
            }
        }
        
        // Update field state
        this.updateFieldState(field, isValid, errorMessage);
        
        // Update form validation state
        this.state.validationErrors[fieldName] = isValid ? null : errorMessage;
        this.updateSubmitButton();
        
        return isValid;
    }

    /**
     * Clear field error on input
     */
    clearFieldError(e) {
        const field = e.target;
        const inputGroup = field.closest('.input-group');
        
        if (inputGroup?.classList.contains('error')) {
            inputGroup.classList.remove('error');
            const errorElement = inputGroup.querySelector('.error-message');
            if (errorElement) {
                errorElement.classList.remove('show');
                errorElement.textContent = '';
            }
        }
    }

    /**
     * Update field visual state
     */
    updateFieldState(field, isValid, errorMessage) {
        const inputGroup = field.closest('.input-group');
        const errorElement = inputGroup?.querySelector('.error-message');
        
        if (inputGroup) {
            inputGroup.classList.toggle('error', !isValid);
        }
        
        if (errorElement) {
            errorElement.textContent = errorMessage || '';
            errorElement.classList.toggle('show', !isValid && errorMessage);
        }
    }

    /**
     * Update submit button state
     */
    updateSubmitButton() {
        if (!this.elements.submitBtn) return;
        
        const hasErrors = Object.values(this.state.validationErrors).some(error => error !== null);
        const termsAccepted = this.elements.termsAgreement?.checked || false;
        const hasItems = this.state.cartItems.length > 0;
        
        const isValid = !hasErrors && termsAccepted && hasItems && !this.state.isLoading;
        
        this.elements.submitBtn.disabled = !isValid;
        
        if (!hasItems) {
            this.elements.submitBtn.querySelector('.btn-text').textContent = 'Cart is Empty';
        } else if (!termsAccepted) {
            this.elements.submitBtn.querySelector('.btn-text').textContent = 'Accept Terms to Continue';
        } else if (hasErrors) {
            this.elements.submitBtn.querySelector('.btn-text').textContent = 'Please Fix Errors';
        }
    }

    /**
     * Handle form submission
     */
    async handleFormSubmit(e) {
        e.preventDefault();
        
        if (this.state.isLoading) return;
        
        // Validate all fields
        const formInputs = this.elements.form.querySelectorAll('input[required], select[required], textarea[required]');
        let isFormValid = true;
        
        formInputs.forEach(input => {
            const isFieldValid = this.validateField({ target: input });
            if (!isFieldValid) isFormValid = false;
        });
        
        if (!isFormValid) {
            this.showError('Please fix the errors in the form');
            return;
        }
        
        // Check minimum order amount
        if (this.state.totals.subtotal < CURRENCY_CONFIG.minimumOrder) {
            this.showError(`Minimum order amount is ${formatPrice(CURRENCY_CONFIG.minimumOrder)}`);
            return;
        }
        
        try {
            this.setLoadingState(true);
            
            // Collect form data
            const formData = new FormData(this.elements.form);
            const orderData = {
                items: this.state.cartItems,
                totals: this.state.totals,
                customer: {
                    name: formData.get('customerName'),
                    email: formData.get('customerEmail'),
                    phone: formData.get('customerPhone')
                },
                delivery: {
                    method: formData.get('deliveryMethod'),
                    address: formData.get('deliveryAddress'),
                    city: formData.get('deliveryCity'),
                    state: formData.get('deliveryState'),
                    notes: formData.get('deliveryNotes')
                },
                payment: {
                    method: formData.get('paymentMethod')
                },
                notes: formData.get('orderNotes'),
                marketing: formData.get('marketingConsent') === 'on'
            };
            
            // Submit order
            const response = await this.submitOrder(orderData);
            
            // Analytics
            if (Analytics.consent) {
                Analytics.trackEcommerce('purchase', {
                    orderId: response.orderId,
                    value: this.state.totals.total,
                    tax: this.state.totals.tax,
                    shipping: this.state.totals.deliveryFee,
                    items: this.state.cartItems
                });
            }
            
            // Clear cart
            await this.clearCart();
            
            // Redirect to confirmation
            window.location.href = `modern-confirmation.html?orderId=${response.orderId}`;
            
        } catch (error) {
            console.error('Order submission failed:', error);
            this.showError(error.message || 'Failed to submit order. Please try again.');
            
            // Analytics
            if (Analytics.consent) {
                Analytics.track('checkout_error', {
                    error: error.message
                });
            }
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * Submit order to server
     */
    async submitOrder(orderData) {
        try {
            const response = await fetch('/api/orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to submit order');
            }

            return await response.json();

        } catch (error) {
            // Mock successful response for demo
            console.warn('API unavailable, using mock response:', error);

            return {
                orderId: 'ORD-' + Date.now(),
                status: 'confirmed',
                estimatedDelivery: new Date(Date.now() + 45 * 60 * 1000).toISOString()
            };
        }
    }

    /**
     * Clear cart after successful order
     */
    async clearCart() {
        try {
            // Try API first
            try {
                await fetch('/api/cart', {
                    method: 'DELETE'
                });
            } catch (apiError) {
                // Fallback to localStorage
                localStorage.removeItem('cartItems');
            }
        } catch (error) {
            console.error('Failed to clear cart:', error);
        }
    }

    /**
     * Set loading state
     */
    setLoadingState(isLoading) {
        this.state.isLoading = isLoading;

        if (this.elements.submitBtn) {
            this.elements.submitBtn.classList.toggle('loading', isLoading);
            this.elements.submitBtn.disabled = isLoading;
        }

        // Disable form inputs during loading
        const formInputs = this.elements.form?.querySelectorAll('input, select, textarea, button');
        formInputs?.forEach(input => {
            if (input !== this.elements.submitBtn) {
                input.disabled = isLoading;
            }
        });
    }

    /**
     * Track checkout start for analytics
     */
    trackCheckoutStart() {
        if (Analytics.consent) {
            Analytics.trackEcommerce('begin_checkout', {
                items: this.state.cartItems,
                value: this.state.totals.total,
                shipping: this.state.totals.deliveryFee,
                tax: this.state.totals.tax
            });
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        if (!this.elements.toastContainer) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="fas fa-${this.getToastIcon(type)}"></i>
            <span>${message}</span>
            <button class="toast-close" aria-label="Close notification">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Close button
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.remove();
        });

        this.elements.toastContainer.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * Get toast icon based on type
     */
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * Handle keyboard navigation
     */
    handleKeyboardNavigation(e) {
        // Escape key to close mobile menu
        if (e.key === 'Escape') {
            const mobileMenu = document.querySelector('.nav-list');
            if (mobileMenu && mobileMenu.classList.contains('active')) {
                this.toggleMobileMenu();
            }
        }

        // Enter key on radio buttons
        if (e.key === 'Enter' && e.target.type === 'radio') {
            e.target.checked = true;
            e.target.dispatchEvent(new Event('change'));
        }
    }

    /**
     * Toggle mobile menu
     */
    toggleMobileMenu() {
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navList = document.querySelector('.nav-list');

        if (mobileMenuToggle && navList) {
            const isExpanded = mobileMenuToggle.getAttribute('aria-expanded') === 'true';

            mobileMenuToggle.setAttribute('aria-expanded', !isExpanded);
            navList.classList.toggle('active');
            document.body.classList.toggle('menu-open');
        }
    }

    /**
     * Initialize accessibility features
     */
    initializeAccessibility() {
        // Add ARIA labels for form sections
        const formSections = document.querySelectorAll('.form-section');
        formSections.forEach((section, index) => {
            const legend = section.querySelector('legend');
            if (legend) {
                const id = `form-section-${index}`;
                legend.id = id;
                section.setAttribute('aria-labelledby', id);
            }
        });

        // Add live region for form validation
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);

        this.liveRegion = liveRegion;

        // Announce form errors
        this.elements.form?.addEventListener('invalid', (e) => {
            e.preventDefault();
            const firstInvalidField = this.elements.form.querySelector(':invalid');
            if (firstInvalidField) {
                firstInvalidField.focus();
                this.announce(`Please fix the error in ${firstInvalidField.labels?.[0]?.textContent || 'the form field'}`);
            }
        }, true);
    }

    /**
     * Announce to screen readers
     */
    announce(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ModernCheckoutManager();
});

// Export for testing
export default ModernCheckoutManager;
