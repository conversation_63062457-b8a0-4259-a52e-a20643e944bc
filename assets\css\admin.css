/* Admin Page Styles */

/* Admin Body */
.admin-body {
    font-family: 'Inter', sans-serif;
    background-color: #f5f7fa;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Admin Login Modal */
.admin-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.admin-modal.active {
    opacity: 1;
    visibility: visible;
}

.admin-modal-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.admin-modal.active .admin-modal-content {
    transform: translateY(0);
}

.admin-login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.admin-logo {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
}

.admin-login-header h2 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.admin-login-header p {
    color: var(--text-light);
    font-size: 0.9rem;
}

.admin-login-form .form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.admin-login-form label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

.admin-login-form input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.admin-login-form input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 5px;
}

.admin-login-btn {
    width: 100%;
    padding: 0.875rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-login-btn:hover {
    background: var(--primary-dark);
}

.admin-login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-loader {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.admin-login-btn.loading .btn-text {
    opacity: 0;
}

.admin-login-btn.loading .btn-loader {
    display: block;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Admin Dashboard */
.admin-dashboard {
    display: none;
    min-height: 100vh;
    background: #f5f7fa;
}

.admin-dashboard.active {
    display: block;
}

/* Admin Header */
.admin-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: white;
    border-bottom: 1px solid #e2e8f0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 2rem;
}

.admin-header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f1f5f9;
}

.admin-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0;
}

.admin-header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Notifications */
.notification-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.notification-btn:hover {
    background: #f1f5f9;
}

.notification-count {
    position: absolute;
    top: 0;
    right: 0;
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* User Menu */
.user-menu-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.user-menu-toggle:hover {
    background: #f1f5f9;
}

.admin-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.admin-name {
    font-weight: 500;
    color: var(--text-color);
}

.user-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.admin-user-menu {
    position: relative;
}

.admin-user-menu:hover .user-menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.user-menu-item:hover {
    background: #f8fafc;
}

.user-menu-divider {
    margin: 0.5rem 0;
    border: none;
    border-top: 1px solid #e2e8f0;
}

/* Admin Sidebar */
.admin-sidebar {
    position: fixed;
    top: 70px;
    left: 0;
    width: 260px;
    height: calc(100vh - 70px);
    background: white;
    border-right: 1px solid #e2e8f0;
    overflow-y: auto;
    z-index: 999;
    transition: transform 0.3s ease;
}

.admin-sidebar.collapsed {
    transform: translateX(-100%);
}

.admin-nav {
    padding: 1rem 0;
}

.admin-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.admin-nav-item {
    margin-bottom: 0.25rem;
}

.admin-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.admin-nav-link:hover {
    background: #f8fafc;
    color: var(--primary-color);
}

.admin-nav-link.active {
    background: #fef3e2;
    color: var(--primary-color);
    border-right: 3px solid var(--primary-color);
}

.admin-nav-link i {
    width: 20px;
    text-align: center;
}

.nav-badge {
    background: var(--primary-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    margin-left: auto;
}

/* Admin Main Content */
.admin-main {
    margin-left: 260px;
    margin-top: 70px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
    transition: margin-left 0.3s ease;
}

.admin-main.expanded {
    margin-left: 0;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.admin-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.admin-section-header h2 {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stat-content h3 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin: 0 0 0.25rem 0;
}

.stat-label {
    color: var(--text-light);
    font-size: 0.875rem;
    margin: 0 0 0.5rem 0;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.stat-change.positive {
    color: #059669;
    background: #d1fae5;
}

.stat-change.negative {
    color: #dc2626;
    background: #fee2e2;
}

.stat-change.neutral {
    color: var(--text-light);
    background: #f3f4f6;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    overflow: hidden;
}

.dashboard-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
}

.dashboard-card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0;
}

.view-all {
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.dashboard-card-content {
    padding: 1.5rem;
}

/* Data Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 0.875rem 1rem;
    text-align: left;
}

.data-table th {
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #f1f5f9;
}

.data-table td {
    color: var(--text-color);
    font-size: 0.9375rem;
    border-bottom: 1px solid #f1f5f9;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

.data-table tbody tr:hover {
    background: #f8fafc;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
}

.status-badge.completed {
    background: #d1fae5;
    color: #059669;
}

.status-badge.processing {
    background: #e0f2fe;
    color: #0284c7;
}

.status-badge.pending {
    background: #fef3c7;
    color: #d97706;
}

.status-badge.cancelled {
    background: #fee2e2;
    color: #dc2626;
}

.status-badge.active {
    background: #d1fae5;
    color: #059669;
}

.status-badge.inactive {
    background: #f3f4f6;
    color: var(--text-light);
}

/* Popular Items List */
.popular-items-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.popular-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.popular-item:last-child {
    border-bottom: none;
}

.popular-item-image {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
}

.popular-item-details {
    flex: 1;
}

.popular-item-details h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0 0 0.25rem 0;
}

.popular-item-details p {
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 500;
    margin: 0;
}

.popular-item-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.popular-item-orders {
    font-size: 0.75rem;
    color: var(--text-light);
}

.popular-item-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--secondary-color);
}

.popular-item-rating i {
    color: #f59e0b;
}

/* Search & Filter */
.search-container {
    position: relative;
}

.search-input {
    padding: 0.625rem 1rem 0.625rem 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9375rem;
    width: 250px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    width: 300px;
}

.search-btn {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
}

.filter-select {
    padding: 0.625rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9375rem;
    color: var(--text-color);
    background-color: white;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Orders Table */
.orders-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.orders-table th:first-child,
.orders-table td:first-child {
    width: 40px;
    text-align: center;
}

.customer-info {
    display: flex;
    flex-direction: column;
}

.customer-name {
    font-weight: 500;
    color: var(--secondary-color);
}

.customer-email {
    font-size: 0.75rem;
    color: var(--text-light);
}

.action-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #f1f5f9;
    color: var(--secondary-color);
}

.view-btn:hover {
    color: #0284c7;
}

.edit-btn:hover {
    color: #059669;
}

.delete-btn:hover {
    color: #dc2626;
}

.message-btn:hover {
    color: var(--primary-color);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1.5rem;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: var(--text-color);
    font-size: 0.9375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-numbers {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-number {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: var(--text-color);
    font-size: 0.9375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-number:hover:not(.active) {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.pagination-number.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination-ellipsis {
    color: var(--text-light);
    font-size: 0.9375rem;
}

/* Menu Management */
.menu-categories {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    overflow: hidden;
}

.category-tabs {
    display: flex;
    border-bottom: 1px solid #f1f5f9;
    padding: 0 1.5rem;
}

.category-tab {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: var(--text-light);
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.category-tab:hover {
    color: var(--text-color);
}

.category-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.menu-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.menu-item-card {
    background: white;
    border: 1px solid #f1f5f9;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.menu-item-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.menu-item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.menu-item-content {
    padding: 1.25rem;
}

.menu-item-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0 0 0.5rem 0;
}

.menu-item-description {
    color: var(--text-light);
    font-size: 0.875rem;
    margin: 0 0 1rem 0;
    line-height: 1.5;
}

.menu-item-details {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.menu-item-price {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-color);
}

.menu-item-category {
    font-size: 0.75rem;
    color: var(--text-light);
    background: #f8fafc;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.menu-item-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.menu-item-orders {
    color: var(--text-light);
}

.menu-item-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--secondary-color);
    font-weight: 500;
}

.menu-item-rating i {
    color: #f59e0b;
}

.menu-item-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggle-btn:hover {
    color: #059669;
}

/* Customers Table */
.customers-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    overflow: hidden;
}

.customer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 0.75rem;
}

.customers-table .customer-info {
    display: flex;
    align-items: center;
}

/* Analytics */
.analytics-overview {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.analytics-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.analytics-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    padding: 1.5rem;
}

.analytics-chart h3,
.analytics-table h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0 0 1.5rem 0;
}

.chart-container {
    position: relative;
    height: 300px;
}

.date-range-picker {
    position: relative;
}

.date-range-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: var(--text-color);
    font-size: 0.9375rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.date-range-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

/* Settings */
.settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    overflow: hidden;
}

.settings-sidebar {
    background: #f8fafc;
    padding: 1.5rem 0;
}

.settings-nav {
    list-style: none;
    margin: 0;
    padding: 0;
}

.settings-nav-item {
    margin-bottom: 0.25rem;
}

.settings-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.settings-nav-link:hover {
    background: #e2e8f0;
    color: var(--primary-color);
}

.settings-nav-link.active {
    background: var(--primary-color);
    color: white;
}

.settings-content {
    padding: 2rem;
}

.settings-panel {
    display: none;
}

.settings-panel.active {
    display: block;
}

.settings-panel h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0 0 1.5rem 0;
}

.settings-form .form-group {
    margin-bottom: 1.5rem;
}

.settings-form label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

.settings-form input,
.settings-form select,
.settings-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9375rem;
    transition: border-color 0.3s ease;
}

.settings-form input:focus,
.settings-form select:focus,
.settings-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.settings-form textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

/* Modal Styles */
.admin-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.admin-modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f1f5f9;
    color: var(--text-color);
}

.admin-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.file-upload {
    position: relative;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 2rem;
    border: 2px dashed #e2e8f0;
    border-radius: 8px;
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-label:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.file-preview {
    margin-top: 1rem;
}

.form-group.checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group.checkbox input {
    width: auto;
}

.form-group.checkbox label {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.show {
        transform: translateX(0);
    }

    .admin-main {
        margin-left: 0;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .analytics-overview,
    .analytics-details {
        grid-template-columns: 1fr;
    }

    .settings-container {
        grid-template-columns: 1fr;
    }

    .settings-sidebar {
        order: 2;
    }

    .settings-content {
        order: 1;
    }
}

@media (max-width: 768px) {
    .admin-header-content {
        padding: 0 1rem;
    }

    .admin-main {
        padding: 1rem;
    }

    .admin-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .search-input {
        width: 200px;
    }

    .search-input:focus {
        width: 250px;
    }

    .menu-items-grid {
        grid-template-columns: 1fr;
    }

    .admin-form .form-row {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .pagination {
        flex-direction: column;
        gap: 1rem;
    }
}
