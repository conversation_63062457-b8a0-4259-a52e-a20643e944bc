/* Terms Page Specific Styles */
.hero-section {
    text-align: center;
    padding: calc(var(--header-height) + var(--spacing-xl)) 0 var(--spacing-xl);
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-xl);
}

.hero-text {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-top: var(--spacing-sm);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    text-align: left;
}

.terms-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-xl);
    position: relative;
    margin-top: var(--spacing-xl);
}

.terms-nav {
    position: sticky;
    top: calc(var(--spacing-xl) + 80px);
    height: fit-content;
    padding: var(--spacing-md);
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.terms-nav ul {
    list-style: none;
    padding: 0;
}

.terms-nav-link {
    display: block;
    padding: var(--spacing-sm);
    color: var(--text-color);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.terms-nav-link:hover,
.terms-nav-link.active {
    background: var(--primary-color);
    color: var(--white);
}

.legal-section {
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.section-icon {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    font-size: 1.5rem;
    color: var(--primary-color);
    opacity: 0.2;
}

.section-subheading {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .terms-container {
        grid-template-columns: 1fr;
    }

    .terms-nav {
        position: relative;
        top: 0;
        margin-bottom: var(--spacing-lg);
    }

    .terms-nav ul {
        display: flex;
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }

    .terms-nav-link {
        white-space: nowrap;
    }
}
