const bcrypt = require('bcrypt');
const crypto = require('crypto');

class PasswordManager {
    static SALT_ROUNDS = 12;
    static MIN_PASSWORD_LENGTH = 12;

    static async hashPassword(password) {
        return bcrypt.hash(password, this.SALT_ROUNDS);
    }

    static async verifyPassword(password, hash) {
        return bcrypt.compare(password, hash);
    }

    static generateResetToken() {
        return crypto.randomBytes(32).toString('hex');
    }

    static validatePassword(password) {
        const hasMinLength = password.length >= this.MIN_PASSWORD_LENGTH;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*]/.test(password);

        return {
            isValid: hasMinLength && hasUpperCase && hasLowerCase && 
                    hasNumbers && hasSpecialChar,
            errors: {
                length: !hasMinLength,
                upperCase: !hasUpperCase,
                lowerCase: !hasLowerCase,
                numbers: !hasNumbers,
                specialChar: !hasSpecialChar
            }
        };
    }
}

module.exports = PasswordManager;