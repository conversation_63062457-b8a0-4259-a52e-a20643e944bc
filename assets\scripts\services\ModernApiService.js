/**
 * Modern API Service
 * Enhanced API handling with retry logic, caching, and better error handling
 */

import { errorHandler } from '../utils/ModernErrorHandler.js';
import ModernUtils from '../utils/ModernUtils.js';

export class ModernApiService {
    constructor(baseURL = '/api', options = {}) {
        this.baseURL = baseURL;
        this.defaultOptions = {
            timeout: 10000,
            retries: 3,
            retryDelay: 1000,
            cache: true,
            cacheTTL: 5 * 60 * 1000, // 5 minutes
            ...options
        };
        
        this.cache = new Map();
        this.pendingRequests = new Map();
        this.interceptors = {
            request: [],
            response: []
        };
        
        this.setupDefaultInterceptors();
    }

    /**
     * Setup default interceptors
     */
    setupDefaultInterceptors() {
        // Request interceptor for authentication
        this.addRequestInterceptor((config) => {
            const token = localStorage.getItem('authToken');
            if (token) {
                config.headers = {
                    ...config.headers,
                    'Authorization': `Bear<PERSON> ${token}`
                };
            }
            return config;
        });

        // Response interceptor for error handling
        this.addResponseInterceptor(
            (response) => response,
            (error) => {
                if (error.status === 401) {
                    // Handle unauthorized access
                    this.handleUnauthorized();
                }
                return Promise.reject(error);
            }
        );
    }

    /**
     * Add request interceptor
     */
    addRequestInterceptor(onFulfilled, onRejected) {
        this.interceptors.request.push({ onFulfilled, onRejected });
    }

    /**
     * Add response interceptor
     */
    addResponseInterceptor(onFulfilled, onRejected) {
        this.interceptors.response.push({ onFulfilled, onRejected });
    }

    /**
     * Apply request interceptors
     */
    async applyRequestInterceptors(config) {
        let processedConfig = config;
        
        for (const interceptor of this.interceptors.request) {
            try {
                if (interceptor.onFulfilled) {
                    processedConfig = await interceptor.onFulfilled(processedConfig);
                }
            } catch (error) {
                if (interceptor.onRejected) {
                    processedConfig = await interceptor.onRejected(error);
                } else {
                    throw error;
                }
            }
        }
        
        return processedConfig;
    }

    /**
     * Apply response interceptors
     */
    async applyResponseInterceptors(response, error = null) {
        for (const interceptor of this.interceptors.response) {
            try {
                if (error && interceptor.onRejected) {
                    response = await interceptor.onRejected(error);
                    error = null; // Error was handled
                } else if (!error && interceptor.onFulfilled) {
                    response = await interceptor.onFulfilled(response);
                }
            } catch (interceptorError) {
                error = interceptorError;
            }
        }
        
        if (error) throw error;
        return response;
    }

    /**
     * Create request configuration
     */
    createConfig(url, options = {}) {
        const config = {
            url: this.resolveURL(url),
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            timeout: this.defaultOptions.timeout,
            cache: this.defaultOptions.cache,
            cacheTTL: this.defaultOptions.cacheTTL,
            retries: this.defaultOptions.retries,
            retryDelay: this.defaultOptions.retryDelay,
            ...options
        };

        return config;
    }

    /**
     * Resolve URL
     */
    resolveURL(url) {
        if (url.startsWith('http')) return url;
        return `${this.baseURL}${url.startsWith('/') ? url : '/' + url}`;
    }

    /**
     * Generate cache key
     */
    generateCacheKey(config) {
        const key = `${config.method}:${config.url}`;
        if (config.method === 'GET' && config.params) {
            const params = new URLSearchParams(config.params).toString();
            return `${key}?${params}`;
        }
        return key;
    }

    /**
     * Get from cache
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;
        
        if (Date.now() > cached.expiry) {
            this.cache.delete(key);
            return null;
        }
        
        return cached.data;
    }

    /**
     * Set cache
     */
    setCache(key, data, ttl) {
        this.cache.set(key, {
            data,
            expiry: Date.now() + ttl
        });
    }

    /**
     * Clear cache
     */
    clearCache(pattern = null) {
        if (pattern) {
            const regex = new RegExp(pattern);
            for (const key of this.cache.keys()) {
                if (regex.test(key)) {
                    this.cache.delete(key);
                }
            }
        } else {
            this.cache.clear();
        }
    }

    /**
     * Create AbortController with timeout
     */
    createAbortController(timeout) {
        const controller = new AbortController();
        
        const timeoutId = setTimeout(() => {
            controller.abort();
        }, timeout);
        
        // Clear timeout if request completes
        controller.signal.addEventListener('abort', () => {
            clearTimeout(timeoutId);
        });
        
        return controller;
    }

    /**
     * Make HTTP request
     */
    async request(config) {
        try {
            // Apply request interceptors
            config = await this.applyRequestInterceptors(config);
            
            // Check cache for GET requests
            if (config.method === 'GET' && config.cache) {
                const cacheKey = this.generateCacheKey(config);
                const cached = this.getFromCache(cacheKey);
                if (cached) {
                    return cached;
                }
                
                // Check for pending request
                if (this.pendingRequests.has(cacheKey)) {
                    return await this.pendingRequests.get(cacheKey);
                }
            }
            
            // Create request promise
            const requestPromise = this.executeRequest(config);
            
            // Store pending request for deduplication
            if (config.method === 'GET' && config.cache) {
                const cacheKey = this.generateCacheKey(config);
                this.pendingRequests.set(cacheKey, requestPromise);
                
                // Clean up pending request when done
                requestPromise.finally(() => {
                    this.pendingRequests.delete(cacheKey);
                });
            }
            
            return await requestPromise;
            
        } catch (error) {
            // Apply response interceptors for errors
            await this.applyResponseInterceptors(null, error);
            throw error;
        }
    }

    /**
     * Execute HTTP request with retry logic
     */
    async executeRequest(config, attempt = 1) {
        const controller = this.createAbortController(config.timeout);
        
        try {
            // Build URL with query parameters
            let url = config.url;
            if (config.params) {
                const params = new URLSearchParams(config.params);
                url += `?${params.toString()}`;
            }
            
            // Prepare fetch options
            const fetchOptions = {
                method: config.method,
                headers: config.headers,
                signal: controller.signal
            };
            
            // Add body for non-GET requests
            if (config.method !== 'GET' && config.data) {
                if (config.data instanceof FormData) {
                    fetchOptions.body = config.data;
                    delete fetchOptions.headers['Content-Type']; // Let browser set it
                } else {
                    fetchOptions.body = JSON.stringify(config.data);
                }
            }
            
            // Make request
            const response = await fetch(url, fetchOptions);
            
            // Handle HTTP errors
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // Parse response
            let data;
            const contentType = response.headers.get('content-type');
            
            if (contentType?.includes('application/json')) {
                data = await response.json();
            } else if (contentType?.includes('text/')) {
                data = await response.text();
            } else {
                data = await response.blob();
            }
            
            // Create response object
            const responseObj = {
                data,
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
                config
            };
            
            // Cache GET responses
            if (config.method === 'GET' && config.cache) {
                const cacheKey = this.generateCacheKey(config);
                this.setCache(cacheKey, responseObj, config.cacheTTL);
            }
            
            // Apply response interceptors
            return await this.applyResponseInterceptors(responseObj);
            
        } catch (error) {
            // Handle timeout
            if (error.name === 'AbortError') {
                error = new Error('Request timeout');
                error.code = 'TIMEOUT';
            }
            
            // Retry logic
            if (attempt < config.retries && this.shouldRetry(error)) {
                await new Promise(resolve => 
                    setTimeout(resolve, config.retryDelay * attempt)
                );
                return this.executeRequest(config, attempt + 1);
            }
            
            // Log error
            errorHandler.handleError({
                type: 'api',
                message: error.message,
                context: {
                    url: config.url,
                    method: config.method,
                    attempt
                }
            });
            
            throw error;
        }
    }

    /**
     * Determine if request should be retried
     */
    shouldRetry(error) {
        // Retry on network errors, timeouts, and 5xx status codes
        return (
            error.code === 'TIMEOUT' ||
            error.message.includes('fetch') ||
            error.message.includes('5')
        );
    }

    /**
     * Handle unauthorized access
     */
    handleUnauthorized() {
        localStorage.removeItem('authToken');
        
        // Redirect to login if not already there
        if (!window.location.pathname.includes('login')) {
            window.location.href = '/login.html';
        }
    }

    /**
     * HTTP method shortcuts
     */
    async get(url, options = {}) {
        return this.request(this.createConfig(url, { ...options, method: 'GET' }));
    }

    async post(url, data, options = {}) {
        return this.request(this.createConfig(url, { 
            ...options, 
            method: 'POST', 
            data,
            cache: false 
        }));
    }

    async put(url, data, options = {}) {
        return this.request(this.createConfig(url, { 
            ...options, 
            method: 'PUT', 
            data,
            cache: false 
        }));
    }

    async patch(url, data, options = {}) {
        return this.request(this.createConfig(url, { 
            ...options, 
            method: 'PATCH', 
            data,
            cache: false 
        }));
    }

    async delete(url, options = {}) {
        return this.request(this.createConfig(url, { 
            ...options, 
            method: 'DELETE',
            cache: false 
        }));
    }

    /**
     * Upload file with progress
     */
    async upload(url, file, options = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        if (options.fields) {
            Object.entries(options.fields).forEach(([key, value]) => {
                formData.append(key, value);
            });
        }
        
        const config = this.createConfig(url, {
            ...options,
            method: 'POST',
            data: formData,
            cache: false
        });
        
        return this.request(config);
    }

    /**
     * Download file
     */
    async download(url, filename, options = {}) {
        const response = await this.request(this.createConfig(url, {
            ...options,
            cache: false
        }));
        
        if (response.data instanceof Blob) {
            ModernUtils.downloadFile(response.data, filename);
        } else {
            throw new Error('Response is not a downloadable file');
        }
        
        return response;
    }

    /**
     * Get API health status
     */
    async healthCheck() {
        try {
            const response = await this.get('/health', { 
                timeout: 5000,
                cache: false 
            });
            return response.data;
        } catch (error) {
            return { status: 'error', message: error.message };
        }
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        const now = Date.now();
        let expired = 0;
        let active = 0;
        
        for (const [key, value] of this.cache.entries()) {
            if (now > value.expiry) {
                expired++;
            } else {
                active++;
            }
        }
        
        return {
            total: this.cache.size,
            active,
            expired,
            hitRate: this.hitRate || 0
        };
    }
}

// Create default instance
const apiService = new ModernApiService();

// Export both class and instance
export { apiService };
export default ModernApiService;
