# Testing Guide

## Test Types

### Unit Tests
Located in `tests/unit/`
```javascript
// Example unit test
describe('CartManager', () => {
  it('should calculate correct totals', () => {
    const cart = new CartManager();
    cart.addItem({ price: 1000, quantity: 2 });
    expect(cart.getTotal()).toBe(2000);
  });
});
```

### Integration Tests
Located in `tests/integration/`
```javascript
// Example integration test
describe('Checkout Process', () => {
  it('should process valid orders', async () => {
    const order = new OrderProcessor();
    const result = await order.process({
      items: [...],
      customer: {...}
    });
    expect(result.status).toBe('success');
  });
});
```

### E2E Tests
Located in `cypress/integration/`
```javascript
// Example E2E test
describe('Menu Page', () => {
  it('should add items to cart', () => {
    cy.visit('/menu');
    cy.get('.menu-item').first().click();
    cy.get('.add-to-cart').click();
    cy.get('.cart-count').should('have.text', '1');
  });
});
```

## Running Tests

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## Writing Tests

1. **Naming Convention**
   - Files: `*.test.js` or `*.spec.js`
   - Test descriptions should be clear and specific

2. **Test Structure**
   - Arrange: Set up test data
   - Act: Execute the code
   - Assert: Check the results

3. **Best Practices**
   - Test one thing per test
   - Use meaningful assertions
   - Mock external dependencies
   - Clean up after tests