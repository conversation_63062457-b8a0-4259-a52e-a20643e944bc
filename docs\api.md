# API Documentation

## Base URL
```
https://api.magicmenu.com
```

## Authentication
All API requests require an API key in the header:
```
Authorization: Bearer YOUR_API_KEY
```

## Endpoints

### Menu Items

#### GET /api/menu
Retrieves all menu items.

**Response**
```json
{
  "items": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "price": "number",
      "category": "string",
      "image": "string",
      "nutrition": {
        "calories": "number",
        "protein": "number",
        "carbs": "number"
      }
    }
  ]
}
```

#### POST /api/orders
Creates a new order.

**Request Body**
```json
{
  "items": [
    {
      "id": "string",
      "quantity": "number"
    }
  ],
  "customerInfo": {
    "name": "string",
    "email": "string",
    "phone": "string",
    "address": "string"
  }
}
```

## Error Handling

All errors follow this format:
```json
{
  "error": {
    "code": "string",
    "message": "string"
  }
}
```

## Rate Limiting
- 1000 requests per hour
- Status 429 when exceeded