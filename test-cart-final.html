<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Cart Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        button {
            background: #ff7a00;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        button:hover {
            background: #e66d00;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .cart-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .cart-item-preview {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        .cart-item-preview:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <h1>🛒 Final Cart Functionality Test</h1>
    
    <div class="test-container">
        <h2>Test Setup</h2>
        <div class="test-grid">
            <button onclick="clearAllData()">Clear All Data</button>
            <button onclick="addSampleItems()">Add Sample Items</button>
            <button onclick="addInvalidItem()">Add Invalid Item</button>
            <button onclick="corruptData()">Corrupt Cart Data</button>
        </div>
        <div id="setup-status"></div>
    </div>

    <div class="test-container">
        <h2>Cart Operations</h2>
        <div class="test-grid">
            <button onclick="testQuantityLimits()">Test Quantity Limits</button>
            <button onclick="testPriceCalculation()">Test Price Calculation</button>
            <button onclick="testErrorRecovery()">Test Error Recovery</button>
            <button onclick="openCartPage()">Open Cart Page</button>
        </div>
        <div id="operations-status"></div>
    </div>

    <div class="test-container">
        <h2>Current Cart Status</h2>
        <div id="cart-preview" class="cart-preview">
            <p>Loading cart data...</p>
        </div>
        <button onclick="refreshCartPreview()">Refresh Preview</button>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // Test data
        const sampleItems = [
            {
                id: 'test-jollof-001',
                title: 'Jollof Rice Special',
                description: 'Premium jollof rice with chicken',
                price: '₦2,500',
                image: 'assets/images/jollof-rice.jpg',
                quantity: 2
            },
            {
                id: 'test-egusi-002',
                title: 'Egusi Soup Deluxe',
                description: 'Rich egusi soup with assorted meat',
                price: '₦3,500',
                image: 'assets/images/egusi.jpg',
                quantity: 1
            },
            {
                id: 'test-amala-003',
                title: 'Amala & Ewedu Combo',
                description: 'Traditional amala with ewedu soup',
                price: '₦2,000',
                image: 'assets/images/amala.jpg',
                quantity: 3
            }
        ];

        function showStatus(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearAllData() {
            try {
                localStorage.removeItem('cartItems');
                showStatus('setup-status', '✅ All cart data cleared successfully', 'success');
                refreshCartPreview();
            } catch (error) {
                showStatus('setup-status', `❌ Error clearing data: ${error.message}`, 'error');
            }
        }

        function addSampleItems() {
            try {
                localStorage.setItem('cartItems', JSON.stringify(sampleItems));
                showStatus('setup-status', `✅ Added ${sampleItems.length} sample items to cart`, 'success');
                refreshCartPreview();
            } catch (error) {
                showStatus('setup-status', `❌ Error adding sample items: ${error.message}`, 'error');
            }
        }

        function addInvalidItem() {
            try {
                const invalidItem = {
                    id: 'invalid-001',
                    title: '', // Invalid: empty title
                    price: 'invalid-price', // Invalid: non-numeric price
                    quantity: -5 // Invalid: negative quantity
                };
                
                const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
                cartItems.push(invalidItem);
                localStorage.setItem('cartItems', JSON.stringify(cartItems));
                
                showStatus('setup-status', '⚠️ Added invalid item to test error handling', 'info');
                refreshCartPreview();
            } catch (error) {
                showStatus('setup-status', `❌ Error adding invalid item: ${error.message}`, 'error');
            }
        }

        function corruptData() {
            try {
                localStorage.setItem('cartItems', 'invalid-json-data{');
                showStatus('setup-status', '⚠️ Cart data corrupted for testing', 'info');
                refreshCartPreview();
            } catch (error) {
                showStatus('setup-status', `❌ Error corrupting data: ${error.message}`, 'error');
            }
        }

        function testQuantityLimits() {
            try {
                const testItem = {
                    id: 'quantity-test-001',
                    title: 'Quantity Test Item',
                    price: '₦1,000',
                    quantity: 150 // Over limit
                };
                
                const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
                cartItems.push(testItem);
                localStorage.setItem('cartItems', JSON.stringify(cartItems));
                
                showStatus('operations-status', '⚠️ Added item with quantity over limit (150)', 'info');
                refreshCartPreview();
            } catch (error) {
                showStatus('operations-status', `❌ Quantity test failed: ${error.message}`, 'error');
            }
        }

        function testPriceCalculation() {
            try {
                const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
                let total = 0;
                let errors = [];
                
                cartItems.forEach(item => {
                    try {
                        const price = parseFloat(item.price?.replace(/[^\d.-]/g, '') || 0);
                        const quantity = parseInt(item.quantity) || 1;
                        
                        if (isNaN(price) || price < 0) {
                            errors.push(`Invalid price for ${item.title}: ${item.price}`);
                        } else {
                            total += price * quantity;
                        }
                    } catch (error) {
                        errors.push(`Error calculating ${item.title}: ${error.message}`);
                    }
                });
                
                const tax = total * 0.05;
                const delivery = 5.00;
                const finalTotal = total + tax + delivery;
                
                let message = `✅ Price calculation completed:<br>
                    Subtotal: ₦${total.toFixed(2)}<br>
                    Tax (5%): ₦${tax.toFixed(2)}<br>
                    Delivery: ₦${delivery.toFixed(2)}<br>
                    Total: ₦${finalTotal.toFixed(2)}`;
                
                if (errors.length > 0) {
                    message += `<br><br>⚠️ Errors found:<br>${errors.join('<br>')}`;
                }
                
                showStatus('operations-status', message, errors.length > 0 ? 'info' : 'success');
            } catch (error) {
                showStatus('operations-status', `❌ Price calculation failed: ${error.message}`, 'error');
            }
        }

        function testErrorRecovery() {
            try {
                // Test localStorage corruption recovery
                localStorage.setItem('cartItems', 'corrupted-data');
                
                // Simulate cart loading with error recovery
                let cartItems = [];
                try {
                    cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
                } catch (parseError) {
                    console.log('Detected corrupted data, clearing...');
                    localStorage.removeItem('cartItems');
                    cartItems = [];
                }
                
                showStatus('operations-status', '✅ Error recovery test passed - corrupted data cleared', 'success');
                refreshCartPreview();
            } catch (error) {
                showStatus('operations-status', `❌ Error recovery test failed: ${error.message}`, 'error');
            }
        }

        function openCartPage() {
            window.open('cart.html', '_blank');
        }

        function refreshCartPreview() {
            try {
                const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
                const preview = document.getElementById('cart-preview');
                
                if (cartItems.length === 0) {
                    preview.innerHTML = '<p>Cart is empty</p>';
                    return;
                }
                
                let html = `<h4>Cart Items (${cartItems.length}):</h4>`;
                cartItems.forEach(item => {
                    const price = item.price || '₦0.00';
                    const quantity = item.quantity || 1;
                    const title = item.title || 'Unknown Item';
                    
                    html += `
                        <div class="cart-item-preview">
                            <div>
                                <strong>${title}</strong><br>
                                <small>ID: ${item.id}</small>
                            </div>
                            <div>
                                ${price} × ${quantity}
                            </div>
                        </div>
                    `;
                });
                
                preview.innerHTML = html;
            } catch (error) {
                document.getElementById('cart-preview').innerHTML = 
                    `<p class="error">Error loading cart: ${error.message}</p>`;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            refreshCartPreview();
            
            // Run basic functionality test
            setTimeout(() => {
                const results = document.getElementById('test-results');
                results.innerHTML = `
                    <div class="status success">
                        ✅ Cart test page loaded successfully<br>
                        ✅ localStorage access working<br>
                        ✅ JSON parsing functional<br>
                        ✅ Error handling in place<br>
                        <br>
                        <strong>Ready for testing!</strong><br>
                        Use the buttons above to test different scenarios, then open the cart page to see the results.
                    </div>
                `;
            }, 500);
        });
    </script>
</body>
</html>
