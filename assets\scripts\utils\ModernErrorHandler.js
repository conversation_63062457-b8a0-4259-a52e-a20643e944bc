/**
 * Modern Error Handler
 * Enhanced error handling with better user experience and debugging
 */

export class ModernErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.maxQueueSize = 50;
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        
        this.setupGlobalErrorHandling();
    }

    /**
     * Setup global error handling
     */
    setupGlobalErrorHandling() {
        // Handle uncaught JavaScript errors
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                stack: event.error?.stack
            });
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled promise rejection',
                error: event.reason,
                stack: event.reason?.stack
            });
        });

        // Handle network errors
        window.addEventListener('offline', () => {
            this.handleNetworkError('offline');
        });

        window.addEventListener('online', () => {
            this.handleNetworkError('online');
        });
    }

    /**
     * Handle different types of errors
     */
    handleError(errorInfo) {
        const error = this.normalizeError(errorInfo);
        
        // Add to error queue
        this.addToQueue(error);
        
        // Log error for debugging
        this.logError(error);
        
        // Show user-friendly message
        this.showUserError(error);
        
        // Report to analytics/monitoring service
        this.reportError(error);
    }

    /**
     * Normalize error information
     */
    normalizeError(errorInfo) {
        const timestamp = new Date().toISOString();
        const userAgent = navigator.userAgent;
        const url = window.location.href;
        
        return {
            id: this.generateErrorId(),
            timestamp,
            userAgent,
            url,
            type: errorInfo.type || 'unknown',
            message: errorInfo.message || 'An unknown error occurred',
            stack: errorInfo.stack,
            filename: errorInfo.filename,
            lineno: errorInfo.lineno,
            colno: errorInfo.colno,
            severity: this.determineSeverity(errorInfo),
            context: this.getErrorContext()
        };
    }

    /**
     * Generate unique error ID
     */
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Determine error severity
     */
    determineSeverity(errorInfo) {
        if (errorInfo.type === 'network' || errorInfo.message?.includes('fetch')) {
            return 'medium';
        }
        
        if (errorInfo.message?.includes('TypeError') || errorInfo.message?.includes('ReferenceError')) {
            return 'high';
        }
        
        if (errorInfo.type === 'validation') {
            return 'low';
        }
        
        return 'medium';
    }

    /**
     * Get error context
     */
    getErrorContext() {
        return {
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            screen: {
                width: screen.width,
                height: screen.height
            },
            connection: navigator.connection ? {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink
            } : null,
            memory: navigator.deviceMemory || null,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        };
    }

    /**
     * Add error to queue
     */
    addToQueue(error) {
        this.errorQueue.push(error);
        
        // Keep queue size manageable
        if (this.errorQueue.length > this.maxQueueSize) {
            this.errorQueue.shift();
        }
    }

    /**
     * Log error for debugging
     */
    logError(error) {
        const logLevel = error.severity === 'high' ? 'error' : 
                        error.severity === 'medium' ? 'warn' : 'info';
        
        console[logLevel]('Error occurred:', {
            id: error.id,
            type: error.type,
            message: error.message,
            stack: error.stack,
            context: error.context
        });
    }

    /**
     * Show user-friendly error message
     */
    showUserError(error) {
        const userMessage = this.getUserFriendlyMessage(error);
        
        // Only show critical errors to users
        if (error.severity === 'high' || error.type === 'network') {
            this.showToast(userMessage, 'error');
        }
    }

    /**
     * Get user-friendly error message
     */
    getUserFriendlyMessage(error) {
        const messages = {
            network: 'Connection problem. Please check your internet and try again.',
            validation: 'Please check your input and try again.',
            authentication: 'Please log in again to continue.',
            authorization: 'You don\'t have permission to perform this action.',
            server: 'Server error. Please try again later.',
            timeout: 'Request timed out. Please try again.',
            default: 'Something went wrong. Please try again.'
        };
        
        // Check for specific error patterns
        if (error.message?.includes('fetch')) {
            return messages.network;
        }
        
        if (error.message?.includes('401')) {
            return messages.authentication;
        }
        
        if (error.message?.includes('403')) {
            return messages.authorization;
        }
        
        if (error.message?.includes('500')) {
            return messages.server;
        }
        
        if (error.message?.includes('timeout')) {
            return messages.timeout;
        }
        
        return messages[error.type] || messages.default;
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'error') {
        const toastContainer = document.getElementById('toast-container') || this.createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="fas fa-${this.getToastIcon(type)}"></i>
            <span>${message}</span>
            <button class="toast-close" aria-label="Close notification">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Close button
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.remove();
        });
        
        toastContainer.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * Create toast container if it doesn't exist
     */
    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container';
        container.setAttribute('aria-live', 'polite');
        container.setAttribute('aria-atomic', 'true');
        document.body.appendChild(container);
        return container;
    }

    /**
     * Get toast icon based on type
     */
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Handle network errors
     */
    handleNetworkError(status) {
        if (status === 'offline') {
            this.showToast('You are offline. Some features may not work.', 'warning');
        } else if (status === 'online') {
            this.showToast('Connection restored.', 'success');
        }
    }

    /**
     * Report error to monitoring service
     */
    async reportError(error) {
        try {
            // Only report high severity errors or in production
            if (error.severity !== 'high' && window.location.hostname === 'localhost') {
                return;
            }
            
            await fetch('/api/errors', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(error)
            });
        } catch (reportError) {
            console.warn('Failed to report error:', reportError);
        }
    }

    /**
     * Retry failed operations
     */
    async retry(operation, context = {}) {
        const key = context.key || 'default';
        const attempts = this.retryAttempts.get(key) || 0;
        
        if (attempts >= this.maxRetries) {
            throw new Error(`Max retry attempts (${this.maxRetries}) exceeded for ${key}`);
        }
        
        try {
            const result = await operation();
            this.retryAttempts.delete(key); // Reset on success
            return result;
        } catch (error) {
            this.retryAttempts.set(key, attempts + 1);
            
            // Exponential backoff
            const delay = Math.pow(2, attempts) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
            
            return this.retry(operation, context);
        }
    }

    /**
     * Wrap async functions with error handling
     */
    wrapAsync(fn, context = {}) {
        return async (...args) => {
            try {
                return await fn.apply(this, args);
            } catch (error) {
                this.handleError({
                    type: context.type || 'async',
                    message: error.message,
                    stack: error.stack,
                    context: context
                });
                throw error;
            }
        };
    }

    /**
     * Wrap functions with error boundaries
     */
    wrapWithBoundary(fn, fallback = null, context = {}) {
        return (...args) => {
            try {
                return fn.apply(this, args);
            } catch (error) {
                this.handleError({
                    type: context.type || 'boundary',
                    message: error.message,
                    stack: error.stack,
                    context: context
                });
                
                return fallback;
            }
        };
    }

    /**
     * Get error statistics
     */
    getErrorStats() {
        const now = Date.now();
        const oneHourAgo = now - (60 * 60 * 1000);
        
        const recentErrors = this.errorQueue.filter(error => 
            new Date(error.timestamp).getTime() > oneHourAgo
        );
        
        const errorsByType = recentErrors.reduce((acc, error) => {
            acc[error.type] = (acc[error.type] || 0) + 1;
            return acc;
        }, {});
        
        const errorsBySeverity = recentErrors.reduce((acc, error) => {
            acc[error.severity] = (acc[error.severity] || 0) + 1;
            return acc;
        }, {});
        
        return {
            total: this.errorQueue.length,
            recent: recentErrors.length,
            byType: errorsByType,
            bySeverity: errorsBySeverity
        };
    }

    /**
     * Clear error queue
     */
    clearErrors() {
        this.errorQueue = [];
        this.retryAttempts.clear();
    }

    /**
     * Export errors for debugging
     */
    exportErrors() {
        const data = JSON.stringify(this.errorQueue, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `errors-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }
}

// Create global instance
const errorHandler = new ModernErrorHandler();

// Export both class and instance
export { errorHandler };
export default ModernErrorHandler;
