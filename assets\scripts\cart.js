// cart.js - Manages the shopping cart functionality

import { CartManager } from './components.js';
import { api } from './api.js';

class CartPageManager {
    constructor() {
        try {
            this.cartManager = new CartManager();
            this.cartItemsContainer = document.querySelector('.cart-items-list');
            this.emptyCartMessage = document.querySelector('.empty-cart-message');
            this.cartSummary = document.querySelector('.cart-summary');
            this.checkoutBtn = document.querySelector('.checkout-btn');
            this.template = document.getElementById('cart-item-template');

            // Validate required elements
            if (!this.cartItemsContainer) {
                console.error('Cart items container not found');
            }
            if (!this.emptyCartMessage) {
                console.error('Empty cart message element not found');
            }
            if (!this.cartSummary) {
                console.error('Cart summary element not found');
            }
            if (!this.template) {
                console.error('Cart item template not found');
            }

            console.log('CartPageManager elements initialized:', {
                cartItemsContainer: !!this.cartItemsContainer,
                emptyCartMessage: !!this.emptyCartMessage,
                cartSummary: !!this.cartSummary,
                checkoutBtn: !!this.checkoutBtn,
                template: !!this.template
            });

            this.init();
        } catch (error) {
            console.error('Error in CartPageManager constructor:', error);
            throw error;
        }
    }

    async init() {
        try {
            // Initialize AOS only if not already initialized
            if (typeof AOS !== 'undefined' && !window.aosInitialized) {
                AOS.init({
                    duration: 800,
                    once: true,
                    offset: 100
                });
                window.aosInitialized = true;
            }

            // Load and display cart
            await this.loadCart();
            this.attachEventListeners();

        } catch (error) {
            console.error('Failed to initialize cart page:', error);
            this.showError('Failed to load cart. Please refresh the page.');
        }
    }

    async loadCart() {
        try {
            // Try to load from API first, fallback to localStorage
            let cartItems = [];

            try {
                const cartData = await api.getCart();
                cartItems = cartData.items || [];
                console.log('Cart loaded from API:', cartItems.length, 'items');
            } catch (apiError) {
                console.warn('API unavailable, using localStorage:', apiError);

                // Try localStorage with error handling
                try {
                    cartItems = CartManager.getCartItems();
                    console.log('Cart loaded from localStorage:', cartItems.length, 'items');
                } catch (storageError) {
                    console.error('Failed to load from localStorage:', storageError);

                    // Try to recover by clearing corrupted data
                    try {
                        localStorage.removeItem('cartItems');
                        cartItems = [];
                        console.log('Cart data was corrupted and has been reset.');
                    } catch (clearError) {
                        console.error('Failed to clear corrupted cart data:', clearError);
                        cartItems = [];
                    }
                }
            }

            // Validate cart items
            cartItems = this.validateCartItems(cartItems);

            // Always render the cart display, even if empty
            this.renderCartItems(cartItems);
            this.updateCartSummary(cartItems);
            this.updateCartDisplay(cartItems);

            // Ensure content is visible
            this.ensureContentVisibility();

            // Double-check visibility after AOS animations
            setTimeout(() => {
                this.ensureContentVisibility();
            }, 1000);

        } catch (error) {
            console.error('Failed to load cart:', error);

            // Fallback to empty cart but still show the page
            this.renderCartItems([]);
            this.updateCartSummary([]);
            this.updateCartDisplay([]);
            this.ensureContentVisibility();

            // Double-check visibility after error handling
            setTimeout(() => {
                this.ensureContentVisibility();
            }, 1000);
        }
    }

    validateCartItems(items) {
        if (!Array.isArray(items)) {
            console.warn('Cart items is not an array, resetting to empty array');
            return [];
        }

        return items.filter(item => {
            // Check required fields
            if (!item || typeof item !== 'object') {
                console.warn('Invalid item object:', item);
                return false;
            }

            if (!item.id || !item.title) {
                console.warn('Item missing required fields:', item);
                return false;
            }

            // Validate quantity
            if (!item.quantity || isNaN(item.quantity) || item.quantity < 1) {
                console.warn('Invalid quantity for item:', item.id, item.quantity);
                item.quantity = 1; // Fix invalid quantity
            }

            // Validate price
            if (!item.price) {
                console.warn('Item missing price:', item.id);
                item.price = '₦0.00'; // Set default price
            }

            return true;
        });
    }

    renderCartItems(cartItems) {
        if (!this.cartItemsContainer || !this.template) {
            console.error('Cart container or template not found');
            return;
        }

        // Clear existing items
        this.cartItemsContainer.innerHTML = '';

        cartItems.forEach(item => {
            const cartItemElement = this.createCartItemElement(item);
            this.cartItemsContainer.appendChild(cartItemElement);
        });
    }

    createCartItemElement(item) {
        try {
            if (!this.template) {
                throw new Error('Cart item template not found');
            }

            const clone = this.template.content.cloneNode(true);
            const cartItem = clone.querySelector('.cart-item');

            if (!cartItem) {
                throw new Error('Cart item element not found in template');
            }

            // Validate item data
            if (!item || !item.id) {
                throw new Error('Invalid item data: missing ID');
            }

            // Set item data
            cartItem.dataset.itemId = item.id;

            // Set image with error handling
            const img = clone.querySelector('.item-image');
            if (img) {
                img.src = item.image || 'assets/images/placeholder.svg';
                img.alt = item.title || 'Food item';

                // Handle image load errors
                img.onerror = function() {
                    this.src = 'assets/images/placeholder.svg';
                    this.onerror = null; // Prevent infinite loop
                };
            }

            // Set title and price with validation
            const titleElement = clone.querySelector('.item-title');
            const priceElement = clone.querySelector('.item-price');

            if (titleElement) {
                titleElement.textContent = item.title || 'Unknown Item';
            }

            if (priceElement) {
                // Validate and format price
                let displayPrice = '₦0.00';
                if (item.price) {
                    try {
                        const numericPrice = parseFloat(item.price.toString().replace(/[^\d.-]/g, ''));
                        if (!isNaN(numericPrice) && numericPrice >= 0) {
                            displayPrice = item.price.toString().includes('₦') ? item.price : `₦${numericPrice.toFixed(2)}`;
                        }
                    } catch (priceError) {
                        console.warn('Error formatting price:', priceError);
                    }
                }
                priceElement.textContent = displayPrice;
            }

            // Set quantity with validation
            const quantityInput = clone.querySelector('.quantity-input');
            if (quantityInput) {
                const quantity = Math.max(1, Math.min(99, parseInt(item.quantity) || 1));
                quantityInput.value = quantity;
                quantityInput.min = '1';
                quantityInput.max = '99';
            }

            return clone;

        } catch (error) {
            console.error('Error creating cart item element:', error);
            // Return a minimal error element
            const errorDiv = document.createElement('div');
            errorDiv.className = 'cart-item cart-error';
            errorDiv.innerHTML = `
                <div class="item-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error loading item</p>
                </div>
            `;
            return errorDiv;
        }
    }

    updateCartDisplay(cartItems) {
        const hasItems = cartItems && cartItems.length > 0;

        if (this.cartItemsContainer) {
            this.cartItemsContainer.style.display = hasItems ? 'block' : 'none';
        }

        if (this.emptyCartMessage) {
            this.emptyCartMessage.style.display = hasItems ? 'none' : 'block';
        }

        // Always show cart summary - it contains order summary even when empty
        if (this.cartSummary) {
            this.cartSummary.style.display = 'block';
        }

        if (this.checkoutBtn) {
            this.checkoutBtn.disabled = !hasItems;
        }

        // Update cart count in header
        const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
        this.cartManager.updateCartCount(totalItems);
    }

    ensureContentVisibility() {
        // Ensure main content areas are visible
        const mainContent = document.querySelector('.main-content');
        const cartHeader = document.querySelector('.cart-header');
        const cartContentWrapper = document.querySelector('.cart-content-wrapper');
        const cartItemsSection = document.querySelector('.cart-items-section');

        if (mainContent) {
            mainContent.style.display = 'block';
            mainContent.style.visibility = 'visible';
            mainContent.style.opacity = '1';
            mainContent.style.transform = 'none'; // Reset any AOS transforms
        }

        if (cartHeader) {
            cartHeader.style.display = 'block';
            cartHeader.style.visibility = 'visible';
            cartHeader.style.opacity = '1';
            cartHeader.style.transform = 'none'; // Reset any AOS transforms
        }

        if (cartContentWrapper) {
            cartContentWrapper.style.display = 'grid';
            cartContentWrapper.style.visibility = 'visible';
            cartContentWrapper.style.opacity = '1';
            cartContentWrapper.style.transform = 'none'; // Reset any AOS transforms
        }

        if (cartItemsSection) {
            cartItemsSection.style.display = 'block';
            cartItemsSection.style.visibility = 'visible';
            cartItemsSection.style.opacity = '1';
            cartItemsSection.style.transform = 'none'; // Reset any AOS transforms
        }

        // Also ensure AOS elements are visible
        const aosElements = document.querySelectorAll('[data-aos]');
        aosElements.forEach(element => {
            element.style.opacity = '1';
            element.style.transform = 'none';
            element.style.visibility = 'visible';
        });

        // Force a repaint
        if (mainContent) {
            mainContent.offsetHeight;
        }
    }

    updateCartSummary(cartItems) {
        const subtotalElement = document.querySelector('.subtotal');
        const taxElement = document.querySelector('.tax');
        const deliveryElement = document.querySelector('.delivery');
        const totalElement = document.querySelector('.total-amount');

        try {
            if (!cartItems || cartItems.length === 0) {
                if (subtotalElement) subtotalElement.textContent = '₦0.00';
                if (taxElement) taxElement.textContent = '₦0.00';
                if (totalElement) totalElement.textContent = '₦5.00'; // Only delivery fee
                return;
            }

            // Calculate totals with error handling
            const subtotal = cartItems.reduce((sum, item) => {
                try {
                    const priceStr = item.price?.toString() || '0';
                    const price = parseFloat(priceStr.replace(/[^\d.-]/g, ''));
                    const quantity = parseInt(item.quantity) || 1;

                    if (isNaN(price) || price < 0) {
                        console.warn(`Invalid price for item ${item.id}: ${item.price}`);
                        return sum;
                    }

                    if (quantity < 1 || quantity > 99) {
                        console.warn(`Invalid quantity for item ${item.id}: ${quantity}`);
                        return sum + (price * 1); // Default to quantity 1
                    }

                    return sum + (price * quantity);
                } catch (error) {
                    console.error(`Error calculating price for item ${item.id}:`, error);
                    return sum;
                }
            }, 0);

            const tax = Math.max(0, subtotal * 0.05); // 5% tax, ensure non-negative
            const delivery = 5.00;
            const total = subtotal + tax + delivery;

            // Update display with formatted values
            if (subtotalElement) subtotalElement.textContent = `₦${subtotal.toFixed(2)}`;
            if (taxElement) taxElement.textContent = `₦${tax.toFixed(2)}`;
            if (deliveryElement) deliveryElement.textContent = `₦${delivery.toFixed(2)}`;
            if (totalElement) totalElement.textContent = `₦${total.toFixed(2)}`;

        } catch (error) {
            console.error('Error updating cart summary:', error);
            // Fallback to safe values
            if (subtotalElement) subtotalElement.textContent = '₦0.00';
            if (taxElement) taxElement.textContent = '₦0.00';
            if (deliveryElement) deliveryElement.textContent = '₦5.00';
            if (totalElement) totalElement.textContent = '₦5.00';
        }
    }

    attachEventListeners() {
        // Use event delegation for dynamically created elements
        if (this.cartItemsContainer) {
            this.cartItemsContainer.addEventListener('click', this.handleCartItemClick.bind(this));
            this.cartItemsContainer.addEventListener('change', this.handleQuantityChange.bind(this));
            this.cartItemsContainer.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));
        }

        // Checkout button
        if (this.checkoutBtn) {
            this.checkoutBtn.addEventListener('click', this.handleCheckout.bind(this));
        }

        // Global keyboard shortcuts
        document.addEventListener('keydown', this.handleGlobalKeyboard.bind(this));
    }

    handleKeyboardNavigation(e) {
        // Handle keyboard navigation within cart items
        if (e.target.classList.contains('quantity-input')) {
            if (e.key === 'Enter') {
                e.target.blur(); // Trigger change event
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                const currentValue = parseInt(e.target.value) || 1;
                if (currentValue < 99) {
                    e.target.value = currentValue + 1;
                    this.handleQuantityChange(e);
                }
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                const currentValue = parseInt(e.target.value) || 1;
                if (currentValue > 1) {
                    e.target.value = currentValue - 1;
                    this.handleQuantityChange(e);
                }
            }
        }

        // Handle remove item with Delete key
        if (e.key === 'Delete' && e.target.closest('.cart-item')) {
            const cartItem = e.target.closest('.cart-item');
            const itemId = cartItem.dataset.itemId;
            if (itemId) {
                this.handleRemoveItem(itemId);
            }
        }
    }

    handleGlobalKeyboard(e) {
        // Global keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'Enter':
                    // Ctrl+Enter to checkout
                    if (this.checkoutBtn && !this.checkoutBtn.disabled) {
                        e.preventDefault();
                        this.handleCheckout();
                    }
                    break;
            }
        }

        // Escape to clear any error messages
        if (e.key === 'Escape') {
            const errorElement = document.querySelector('.cart-error');
            const successElement = document.querySelector('.cart-success');
            if (errorElement) errorElement.style.display = 'none';
            if (successElement) successElement.style.display = 'none';
        }
    }

    handleCartItemClick(e) {
        const target = e.target;
        const cartItem = target.closest('.cart-item');

        if (!cartItem) return;

        const itemId = cartItem.dataset.itemId;

        if (target.closest('.quantity-btn')) {
            this.handleQuantityButton(target, cartItem, itemId);
        } else if (target.closest('.remove-item-btn')) {
            this.handleRemoveItem(itemId);
        }
    }

    handleQuantityButton(button, cartItem, itemId) {
        try {
            if (!button || !cartItem || !itemId) {
                throw new Error('Invalid parameters for quantity button handler');
            }

            const isIncrease = button.classList.contains('increase');
            const quantityInput = cartItem.querySelector('.quantity-input');

            if (!quantityInput) {
                throw new Error('Quantity input not found');
            }

            const currentQuantity = parseInt(quantityInput.value) || 1;
            let newQuantity;

            if (isIncrease) {
                newQuantity = Math.min(99, currentQuantity + 1); // Max 99 items
            } else {
                newQuantity = Math.max(1, currentQuantity - 1); // Min 1 item
            }

            // Update input value immediately for better UX
            quantityInput.value = newQuantity;

            // Disable buttons at limits
            const decreaseBtn = cartItem.querySelector('.quantity-btn.decrease');
            const increaseBtn = cartItem.querySelector('.quantity-btn.increase');

            if (decreaseBtn) decreaseBtn.disabled = newQuantity <= 1;
            if (increaseBtn) increaseBtn.disabled = newQuantity >= 99;

            this.updateItemQuantity(itemId, newQuantity);

        } catch (error) {
            console.error('Error handling quantity button:', error);
            this.showError('Failed to update quantity');
        }
    }

    handleQuantityChange(e) {
        try {
            if (!e.target.classList.contains('quantity-input')) {
                return;
            }

            const cartItem = e.target.closest('.cart-item');
            if (!cartItem) {
                throw new Error('Cart item not found');
            }

            const itemId = cartItem.dataset.itemId;
            if (!itemId) {
                throw new Error('Item ID not found');
            }

            let newQuantity = parseInt(e.target.value);

            // Validate quantity
            if (isNaN(newQuantity) || newQuantity < 1) {
                newQuantity = 1;
            } else if (newQuantity > 99) {
                newQuantity = 99;
            }

            // Update input value to validated value
            e.target.value = newQuantity;

            // Update button states
            const decreaseBtn = cartItem.querySelector('.quantity-btn.decrease');
            const increaseBtn = cartItem.querySelector('.quantity-btn.increase');

            if (decreaseBtn) decreaseBtn.disabled = newQuantity <= 1;
            if (increaseBtn) increaseBtn.disabled = newQuantity >= 99;

            this.updateItemQuantity(itemId, newQuantity);

        } catch (error) {
            console.error('Error handling quantity change:', error);
            this.showError('Failed to update quantity');
        }
    }

    async updateItemQuantity(itemId, quantity) {
        try {
            // Update in CartManager
            this.cartManager.updateQuantity(itemId, quantity);

            // Reload cart display
            await this.loadCart();

        } catch (error) {
            console.error('Failed to update quantity:', error);
            this.showError('Failed to update item quantity.');
        }
    }

    async handleRemoveItem(itemId) {
        try {
            // Find the item for confirmation dialog
            const cartItems = CartManager.getCartItems();
            const item = cartItems.find(item => item.id === itemId);
            const itemName = item ? item.title : 'this item';

            // Show confirmation dialog
            if (!confirm(`Are you sure you want to remove "${itemName}" from your cart?`)) {
                return;
            }

            // Add loading state to the item
            const cartItemElement = document.querySelector(`[data-item-id="${itemId}"]`);
            if (cartItemElement) {
                cartItemElement.classList.add('loading');
                cartItemElement.style.opacity = '0.6';
                cartItemElement.style.pointerEvents = 'none';
            }

            // Remove from CartManager
            this.cartManager.removeItem(itemId);

            // Show success feedback
            this.showSuccess(`"${itemName}" removed from cart`);

            // Reload cart display
            await this.loadCart();

        } catch (error) {
            console.error('Failed to remove item:', error);
            this.showError('Failed to remove item from cart.');

            // Remove loading state on error
            const cartItemElement = document.querySelector(`[data-item-id="${itemId}"]`);
            if (cartItemElement) {
                cartItemElement.classList.remove('loading');
                cartItemElement.style.opacity = '';
                cartItemElement.style.pointerEvents = '';
            }
        }
    }

    handleCheckout() {
        try {
            const cartItems = CartManager.getCartItems();

            if (!cartItems || cartItems.length === 0) {
                this.showError('Your cart is empty. Add some items before checkout.');
                return;
            }

            // Validate cart items before checkout
            const validItems = this.validateCartItems(cartItems);
            if (validItems.length !== cartItems.length) {
                this.showError('Some items in your cart are invalid. Please review your cart.');
                return;
            }

            // Calculate total to ensure minimum order
            const subtotal = validItems.reduce((sum, item) => {
                const price = parseFloat(item.price?.replace(/[^\d.-]/g, '') || 0);
                return sum + (price * (item.quantity || 1));
            }, 0);

            const minimumOrder = 10.00; // Minimum order amount
            if (subtotal < minimumOrder) {
                this.showError(`Minimum order amount is ₦${minimumOrder.toFixed(2)}. Current total: ₦${subtotal.toFixed(2)}`);
                return;
            }

            // Add loading state to checkout button
            if (this.checkoutBtn) {
                this.checkoutBtn.disabled = true;
                this.checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            }

            // Small delay for better UX
            setTimeout(() => {
                window.location.href = 'checkout.html';
            }, 500);

        } catch (error) {
            console.error('Checkout error:', error);
            this.showError('Failed to proceed to checkout. Please try again.');

            // Reset checkout button
            if (this.checkoutBtn) {
                this.checkoutBtn.disabled = false;
                this.checkoutBtn.innerHTML = '<span>Proceed to Checkout</span><i class="fas fa-arrow-right"></i>';
            }
        }
    }

    showSuccess(message) {
        // Create or update success message
        let successElement = document.querySelector('.cart-success');

        if (!successElement) {
            successElement = document.createElement('div');
            successElement.className = 'cart-success';

            const container = document.querySelector('.cart-items-container');
            if (container) {
                container.insertBefore(successElement, container.firstChild);
            }
        }

        successElement.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <p>${message}</p>
        `;

        successElement.style.display = 'block';

        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (successElement.parentNode) {
                successElement.style.display = 'none';
            }
        }, 3000);
    }

    showError(message) {
        // Create or update error message
        let errorElement = document.querySelector('.cart-error');

        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'cart-error';

            const container = document.querySelector('.cart-items-container');
            if (container) {
                container.insertBefore(errorElement, container.firstChild);
            }
        }

        errorElement.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <p>${message}</p>
        `;

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorElement.parentNode) {
                errorElement.remove();
            }
        }, 5000);
    }
}

// Initialize cart page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if we're on the cart page
    if (document.querySelector('.cart-items-section')) {
        try {
            console.log('Initializing cart page...');
            console.log('Cart elements found:', {
                cartItemsSection: !!document.querySelector('.cart-items-section'),
                mainContent: !!document.querySelector('.main-content'),
                cartHeader: !!document.querySelector('.cart-header'),
                cartContentWrapper: !!document.querySelector('.cart-content-wrapper'),
                emptyCartMessage: !!document.querySelector('.empty-cart-message'),
                cartSummary: !!document.querySelector('.cart-summary')
            });

            new CartPageManager();

            // Add a global flag to indicate cart page is loaded
            window.cartPageLoaded = true;
            console.log('Cart page initialization completed');

        } catch (error) {
            console.error('Failed to initialize cart page:', error);

            // Fallback: ensure basic content visibility
            const mainContent = document.querySelector('.main-content');
            const emptyCartMessage = document.querySelector('.empty-cart-message');
            const cartHeader = document.querySelector('.cart-header');
            const cartContentWrapper = document.querySelector('.cart-content-wrapper');

            console.log('Applying fallback visibility fixes...');

            if (mainContent) {
                mainContent.style.display = 'block';
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                console.log('Main content visibility restored');
            }

            if (cartHeader) {
                cartHeader.style.display = 'block';
                cartHeader.style.visibility = 'visible';
                cartHeader.style.opacity = '1';
                console.log('Cart header visibility restored');
            }

            if (cartContentWrapper) {
                cartContentWrapper.style.display = 'grid';
                cartContentWrapper.style.visibility = 'visible';
                cartContentWrapper.style.opacity = '1';
                console.log('Cart content wrapper visibility restored');
            }

            if (emptyCartMessage) {
                emptyCartMessage.style.display = 'block';
                console.log('Empty cart message visibility restored');
            }

            window.cartPageLoaded = false;
        }
    } else {
        console.log('Cart page elements not found - not on cart page');
    }
});

