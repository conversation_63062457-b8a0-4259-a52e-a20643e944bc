// Cart Debug Script
// Run this in the browser console on the cart.html page to diagnose issues

console.log('=== CART PAGE DEBUG SCRIPT ===');

// Check if we're on the cart page
const isCartPage = !!document.querySelector('.cart-items-section');
console.log('Is cart page:', isCartPage);

if (!isCartPage) {
    console.error('This script should be run on the cart.html page');
} else {
    console.log('✓ Running on cart page');
}

// Check for required elements
const elements = {
    mainContent: document.querySelector('.main-content'),
    cartHeader: document.querySelector('.cart-header'),
    cartContentWrapper: document.querySelector('.cart-content-wrapper'),
    cartItemsSection: document.querySelector('.cart-items-section'),
    cartItemsContainer: document.querySelector('.cart-items-container'),
    cartItemsList: document.querySelector('.cart-items-list'),
    emptyCartMessage: document.querySelector('.empty-cart-message'),
    cartSummary: document.querySelector('.cart-summary'),
    checkoutBtn: document.querySelector('.checkout-btn'),
    template: document.getElementById('cart-item-template')
};

console.log('=== ELEMENT AVAILABILITY ===');
Object.entries(elements).forEach(([name, element]) => {
    if (element) {
        console.log(`✓ ${name}: Found`);
    } else {
        console.error(`✗ ${name}: NOT FOUND`);
    }
});

// Check element visibility
console.log('=== ELEMENT VISIBILITY ===');
Object.entries(elements).forEach(([name, element]) => {
    if (element) {
        const styles = window.getComputedStyle(element);
        const isVisible = styles.display !== 'none' && 
                         styles.visibility !== 'hidden' && 
                         styles.opacity !== '0';
        
        console.log(`${name}:`, {
            display: styles.display,
            visibility: styles.visibility,
            opacity: styles.opacity,
            isVisible: isVisible
        });
        
        if (!isVisible) {
            console.warn(`⚠️ ${name} may not be visible`);
        }
    }
});

// Check localStorage cart data
console.log('=== CART DATA ===');
try {
    const cartData = localStorage.getItem('cartItems');
    if (cartData) {
        const items = JSON.parse(cartData);
        console.log('Cart items in localStorage:', items);
        console.log('Number of items:', items.length);
        
        const totalItems = items.reduce((sum, item) => sum + (item.quantity || 1), 0);
        console.log('Total quantity:', totalItems);
        
        const totalValue = items.reduce((sum, item) => {
            const price = parseFloat(item.price?.replace(/[^\d.-]/g, '') || 0);
            return sum + (price * (item.quantity || 1));
        }, 0);
        console.log('Total value:', totalValue);
    } else {
        console.log('No cart data in localStorage');
    }
} catch (error) {
    console.error('Error reading cart data:', error);
}

// Check if CartPageManager is loaded
console.log('=== SCRIPT STATUS ===');
console.log('CartPageManager loaded:', typeof window.CartPageManager !== 'undefined');
console.log('Cart page loaded flag:', window.cartPageLoaded);
console.log('AOS initialized flag:', window.aosInitialized);

// Check for JavaScript errors
console.log('=== ERROR CHECKING ===');
const originalError = console.error;
let errorCount = 0;
console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
};

// Check AOS
if (typeof AOS !== 'undefined') {
    console.log('✓ AOS library loaded');
    try {
        AOS.refresh();
        console.log('✓ AOS refresh successful');
    } catch (error) {
        console.error('AOS refresh failed:', error);
    }
} else {
    console.warn('⚠️ AOS library not loaded');
}

// Force visibility fix function
window.forceCartVisibility = function() {
    console.log('=== FORCING CART VISIBILITY ===');
    
    const mainContent = document.querySelector('.main-content');
    const cartHeader = document.querySelector('.cart-header');
    const cartContentWrapper = document.querySelector('.cart-content-wrapper');
    const cartItemsSection = document.querySelector('.cart-items-section');
    const emptyCartMessage = document.querySelector('.empty-cart-message');
    
    [mainContent, cartHeader, cartContentWrapper, cartItemsSection].forEach(element => {
        if (element) {
            element.style.display = element === cartContentWrapper ? 'grid' : 'block';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
            element.style.transform = 'none';
            console.log('Fixed visibility for:', element.className);
        }
    });
    
    // Show empty cart message if no items
    const cartData = localStorage.getItem('cartItems');
    const hasItems = cartData && JSON.parse(cartData).length > 0;
    
    if (emptyCartMessage) {
        emptyCartMessage.style.display = hasItems ? 'none' : 'block';
        console.log('Empty cart message display:', hasItems ? 'none' : 'block');
    }
    
    // Force repaint
    if (mainContent) {
        mainContent.offsetHeight;
    }
    
    console.log('✓ Visibility fix applied');
};

// Add test data function
window.addTestCartData = function() {
    const testItems = [
        {
            id: 'debug-test-1',
            title: 'Debug Test Pizza',
            price: '₦2500.00',
            quantity: 1,
            image: 'assets/images/placeholder.svg'
        }
    ];
    
    localStorage.setItem('cartItems', JSON.stringify(testItems));
    console.log('Test cart data added');
    
    // Reload the page to see changes
    if (confirm('Test data added. Reload page to see changes?')) {
        location.reload();
    }
};

console.log('=== DEBUG FUNCTIONS AVAILABLE ===');
console.log('Run forceCartVisibility() to fix visibility issues');
console.log('Run addTestCartData() to add test data and reload');

console.log('=== DEBUG SCRIPT COMPLETE ===');
console.log('Total errors detected:', errorCount);
